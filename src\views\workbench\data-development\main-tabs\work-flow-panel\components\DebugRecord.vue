<template>
  <t-drawer :visible.sync="visible" header="调试记录" placement="bottom" :footer="false" :closeBtn="true" destroyOnClose showInAttachedElement :sizeDraggable="true" @close="handleClose">
    <div class="debug-record-wrap">
      <div class="aside">
        <p class="title">任务节点</p>
        <div class="list-wrap">
          <t-list :scroll="{ type: 'virtual' }">
            <t-list-item v-for="(item, index) in debugList" :class="{ 'actived': currentIndex === index }" :key="index" @click="handleClick(item, index)">
              <div style="width: 100%; display: flex; align-items: center;">
                <span class="status" :style="{ color: getStatusColor(item.status), 'background-color': getStatusBgColor(item.status) }">{{ getStatusLabel(item.status) }}</span>
                <span class="label">
                  <ellipsis-with-tooltip :text="item.taskName"></ellipsis-with-tooltip>
                </span>
                <t-tooltip content="停止" v-if="item.status === 'RUNNING'">
                  <span class="operate" @click.stop="handleStop(item)"><StopCircleIcon/></span>
                </t-tooltip>
              </div>
            </t-list-item>
          </t-list>
        </div>
      </div>
      <div class="main">
        <p class="title">任务节点 {{ debugList[currentIndex]?.taskName }} 调试详情</p>
        <div class="log-list-wrap">
          <t-skeleton :loading="loading">
            <p v-for="(item, index) in debugLogArray" :key="index" class="log-list-item">{{ item }}</p>
          </t-skeleton>
        </div>
      </div>
    </div>
  </t-drawer>
</template>

<script setup>
import { ref, defineProps, defineExpose, onUnmounted } from 'vue';
import { useDataDevelopmentStore } from '@/stores/data-development';
import EllipsisWithTooltip from '@/components/EllipsisWithTooltip.vue';
import { StopCircleIcon } from 'tdesign-icons-vue';
import { MessagePlugin } from 'tdesign-vue';

const props = defineProps({
  spaceId: {
    type: String,
    default: '',
  },
  processCode: {
    type: String,
    default: '',
  },
});

const dataDevelopmentStore = useDataDevelopmentStore();

let timerId = null;

const visible = ref(false);

const debugList = ref([]);

const debugLogArray = ref([]);

const currentIndex = ref(null);

const loading = ref(false);

async function open(data) {
  console.log('[ 调试弹窗 ] >', data);
  visible.value = true;
  await fetchDebugList();
  fetchLoop();
}

async function handleClick(item, index) {
  currentIndex.value = index;
  loading.value = true;
  const { data } = await dataDevelopmentStore.fetchFlowDebugLog({ spaceId: props.spaceId, processCode: props.processCode, jobName: item.jobName }, { resourceGroupId: item.resourceGroupId });
  loading.value = false;
  debugLogArray.value = data.split('\n');
}

async function fetchDebugList(options = { loading: true }) {
  const { data } = await dataDevelopmentStore.fetchFlowDebugList({ spaceId: props.spaceId, processCode: props.processCode }, options);
  debugList.value = data;
}

function fetchLoop() {
  timerId = setInterval(async () => {
    if (debugList.value.every(item => item.status !== 'RUNNING')) {
      clearImmediate(timerId);
      return;
    }
    await fetchDebugList({ loading: false });
  }, 2000);
}

function handleClose() {
  clearInterval(timerId);
  debugList.value = [];
  debugLogArray.value = [];
  currentIndex.value = null;
}

function getStatusLabel(status) {
  return {
    RUNNING: '运行中',
    SUCCESS: '成功',
    FAILURE: '失败',
  }[status];
}

function getStatusColor(status) {
  return {
    RUNNING: '#125FFF',
    SUCCESS: '#50BA5E',
    FAILURE: '#F81D22',
  }[status];
}

function getStatusBgColor(status) {
  return {
    RUNNING: '#E7EEFF',
    SUCCESS: '#D9FFDE',
    FAILURE: '#FFE6E7',
  }[status];
}

async function handleStop(row) {
  await dataDevelopmentStore.fetchFlowDebugStop({ spaceId: props.spaceId, processCode: props.processCode, jobName: row.jobName }, { resourceGroupId: row.resourceGroupId });
  MessagePlugin.success('操作成功');
  // eslint-disable-next-line no-param-reassign
  row.status = 'FAILURE';
}

onUnmounted(() => {
  handleClose();
});

defineExpose({
  open,
});
</script>

<style lang="less" scoped>
.debug-record-wrap {
  height: 100%;
  display: flex;
  .aside {
    width: 400px;
    border-right: 1px solid #e8e8e8;
    padding: 8px 16px 16px 16px;
    .list-wrap {
      height: calc(100% - 38px);
      overflow: auto;
      .t-list {
        height: 100%;
        .t-list-item {
          padding: 0;
          margin-bottom: 8px;
          height: 24px;
          &:last-child {
            margin-bottom: 0;
          }
          .status {
            width: 50px;
            text-align: center;
            margin-right: 5px;
            font-size: 12px;
            padding: 0 5px;
          }
          .label {
            width: 278px;
            cursor: pointer;
            margin-right: 5px;
          }
          .operate {
            cursor: pointer;
          }
        }
      }
      .actived {
        color: var(--des-color-theme);
      }
    }
  }
  .main {
    padding: 8px 16px 16px 16px;
    flex: 1;
    .log-list-wrap {
      height: calc(100% - 38px);
      overflow: auto;
      width: 100%;
      .log-list-item {
        word-break: break-all;
        word-wrap: break-word;
        overflow: hidden;
        min-height: 24px;
      }
    }
  }
  .title {
    margin-bottom: 16px;
  }
}
:deep(.t-drawer__body) {
  padding: 0 !important;
}
</style>
