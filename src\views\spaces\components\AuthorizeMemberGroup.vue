<script setup>
import { ref, defineExpose, defineProps } from 'vue';
import { omit } from 'lodash';
import BasicDialog from '@/components/BasicDialog.vue';
import AuthorizeTree from './authorize/AuthorizeTree.vue';
import { InfoCircleIcon } from 'tdesign-icons-vue';
import { useForm } from '@/hooks';
import { useSpacesStore } from '@/stores/spaces';
import EllipsisWithTooltip from '@/components/EllipsisWithTooltip.vue';
import { MessagePlugin } from 'tdesign-vue';

const props = defineProps({
  spaceId: {
    type: String,
    default: '',
  },
  memberGroupOptions: {
    type: Array,
    default: () => [],
  },
});

const spaceStore = useSpacesStore();

const visible = ref(false);

const authDataDevType = ref('flow');

const initFormData = {
  id: '',
  name: '',
  code: '',
  authType: 'dataDevelopment',
  authAction: 'directAuthorise',
  transferPersons: [],
};
const initFormRules = {
  code: [{ required: true, message: '请选择用户组' }],
  authAction: [{ required: true }],
  authType: [{ required: true }],
  transferPersons: [{ required: true, message: '请选择转移对象' }],
  user: [{ required: true, message: '请选择转移用户' }],
  userGroup: [{ required: true, message: '请选择转移用户组' }],
};

const { form: formData, rules, formRef, validateForm, resetData } = useForm(initFormData, initFormRules);
// 数据目录数据
const data = ref([
  { key: '1', type: 'source', label: 'SR数据源1', permissions: [], children: [{ key: '1-1', type: 'database', label: 'retail_e_com', permissions: [], children: [{ key: '1-1-1', type: 'table', label: '表', permissions: [], children: [{ key: '1-1-1-1', type: 'fleid', label: 'retail_e_commer1', permissions: [] }] }] }] },
  { key: '2', type: 'source', label: 'SR数据源2', permissions: [], children: [{ key: '2-1', type: 'database', label: 'retail_e', permissions: [] }] },
]);
function getIcon(type) {
  let icon;
  switch (type) {
    // 数据源
    case 'source':
      icon = 'des-micon-shujuyuan';
      break;
    // 数据库
    case 'database':
      icon = 'des-micon-shujuku';
      break;
    // 表
    case 'table':
      icon = 'des-micon-biao';
      break;
    // 视图
    case 'view':
      icon = 'des-micon-shitu';
      break;
    // 函数
    case 'function':
      icon = 'des-micon-hanshu';
      break;
    case 'fleid':
      icon = 'des-icon-biao';
      break;
    default:
      icon = '';
  }
  return icon;
}
function getAuthOptions(type) {
  let options = [
    { label: '编辑', value: 'edit' },
    { label: '使用', value: 'use' },
  ];
  if (type === 'database') {
    options = [
      { label: '编辑', value: 'edit' },
      { label: '创建表/视图', value: 'createTable' },
      { label: '创建物化视图', value: 'createView' },
      { label: '创建函数', value: 'createFunction' },
    ];
  }
  if (type === 'table') {
    options = [
      { label: '编辑', value: 'edit' },
      { label: '可写', value: 'write' },
      { label: '可读', value: 'read' },
    ];
  }
  return options;
}
// 工作流数据
const flowData = ref([]);
// Git项目数据
const gitData = ref([]);

async function getFlowAuthDetail() {
  const { data } = await spaceStore.getSpacesFlowAuthDetail(props.spaceId, formData.id, { granteeTypeEnum: 'GROUP' });
  flowData.value = data?.map(item => ({
    ...item,
    label: item.processDefinitionName, // 用于树的搜索
    groupPermissionEnums: item.groupPermissionEnums || [],
  }));
}
async function getGitAuthDetail() {
  const { data } = await spaceStore.getSpacesGitAuthDetail(props.spaceId, formData.id, { granteeTypeEnum: 'GROUP' });
  gitData.value = data?.map(item => ({
    ...item,
    label: item.gitProjectName, // 用于树的搜索
    groupPermissionEnums: item.groupPermissionEnums || [],
  }));
}

function flowCheckAll() {
  // eslint-disable-next-line no-param-reassign
  flowData.value.forEach(item => item.groupPermissionEnums = ['EDIT', 'RUN', 'READ', 'GRANT']);
}
function flowCheckReset() {
  // eslint-disable-next-line no-param-reassign
  flowData.value.forEach(item => item.groupPermissionEnums = []);
}
function gitCheckAll() {
  // eslint-disable-next-line no-param-reassign
  gitData.value.forEach(item => item.groupPermissionEnums = ['EDIT', 'USE', 'GRANT']);
}
function gitCheckReset() {
  // eslint-disable-next-line no-param-reassign
  gitData.value.forEach(item => item.groupPermissionEnums = []);
}

function handleClose() {
  close();
}
function getFlowSubData(basicParams) {
  return {
    ...basicParams,
    processPermissionList: flowData.value.map(item => ({
      ...omit(item, ['label']),
      permissionList: item.groupPermissionEnums,
    })),
  };
}
function getGitSubData(basicParams) {
  return {
    ...basicParams,
    gitProjectPermissionList: gitData.value.map(item => ({
      ...omit(item, ['label']),
      permissionList: item.groupPermissionEnums,
    })),
  };
}
async function handleConfirm() {
  const success = await validateForm();
  if (!success) return;
  // 数据目录的授权逻辑
  if (formData.authType === 'dataDirectory') {
    return;
  }
  // 数据开发-准备所有需要并行执行的异步操作
  const asyncOperations = [];
  const basicParams = {
    spaceId: props.spaceId,
    user: {
      staffId: formData.id,
    },
    granteeType: 'GROUP',
  };
  // 工作流授权
  if (flowData.value.length) {
    const subData = getFlowSubData(basicParams);
    asyncOperations.push(spaceStore.setSpacesFlowAuth(subData));
  }
  // Git授权
  if (gitData.value.length) {
    const subData = getGitSubData(basicParams);
    asyncOperations.push(spaceStore.setSpacesGitAuth(subData));
  }
  // 并行执行所有异步操作
  await Promise.all(asyncOperations);
  MessagePlugin.success('操作成功');
  handleClose();
}

function handleChangeUserGroup(value) {
  const target = props.memberGroupOptions.find(item => item.value === value);
  formData.id = target.id;
  formData.name = target.name;
  formData.code = target.code;
  getFlowAuthDetail();
  getGitAuthDetail();
}

function open(data) {
  visible.value = true;
  formData.id = data.id;
  formData.name = data.name;
  formData.code = data.code;
  getFlowAuthDetail();
  getGitAuthDetail();
}
function close() {
  visible.value = false;
  flowData.value = [];
  gitData.value = [];
  authDataDevType.value = 'flow';
  resetData();
}

function handleChangeFlowPermission(data, value, context) {
  const { current, type } = context;
  const array = value.filter(item => item === 'GRANT');
  if (current === 'RUN') {
    Object.assign(data, {
      groupPermissionEnums: type === 'check' ? ['RUN', 'EDIT', 'READ', ...array] : [...array],
    });
  }
  if (current === 'EDIT') {
    Object.assign(data, {
      groupPermissionEnums: type === 'check' ? ['EDIT', 'READ', ...array] : [...array],
    });
  }
}

function hasSameAuth(targetArray, array) {
  return array.some(element => targetArray.includes(element));
}

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="900"
    :visible.sync="visible"
    header="授权"
    :onClosed="handleClose"
    @handleClose="handleClose"
    @handleConfirm="handleConfirm"
  >
    <t-form :data="formData" :rules="rules" ref="formRef" label-width="88px">
      <t-form-item label="用户组" name="code">
        <t-select v-model="formData.code" :options="memberGroupOptions" style="width: 200px;" @change="handleChangeUserGroup"></t-select>
      </t-form-item>
      <t-form-item label="授权" name="authType">
        <t-radio-group v-model="formData.authType">
          <t-radio-button value="dataDirectory" disabled>数据目录</t-radio-button>
          <t-radio-button value="dataDevelopment">数据开发</t-radio-button>
        </t-radio-group>
      </t-form-item>
      <t-form-item>
        <div class="no-label-item">
          <template v-if="formData.authType === 'dataDirectory'">
            <authorize-tree :data="data" name="数据内容">
              <template #label="{ node }">
                <t-space :size="4">
                  <des-icon :name="getIcon(node.data.type)" size="14" />
                  <span>{{node.label}}</span>
                </t-space>
              </template>
              <template #operations="{ node }">
                <t-checkbox-group v-model="node.data.permissions">
                  <t-checkbox v-for="item in getAuthOptions(node.data.type)" :label="item.label" :value="item.value" :key="item.value"></t-checkbox>
                </t-checkbox-group>
              </template>
            </authorize-tree>
          </template>
          <template v-else>
            <t-tabs v-model="authDataDevType">
              <!-- 默认插槽 和 具名插槽（panel）都是用来渲染面板内容 -->
              <t-tab-panel value="flow" label="工作流">
                <authorize-tree :data="flowData" name="工作流" @check="flowCheckAll" @reset="flowCheckReset">
                  <template #label="{ node }">
                    <div class="des-flex-align-center flex-wrap">
                      <des-icon name="des-micon-gongzuoliu" size="14" class="icon"/>
                      <span class="text">
                        <ellipsis-with-tooltip :text="node.data?.processDefinitionName"></ellipsis-with-tooltip>
                      </span>
                    </div>
                  </template>
                  <template #operations="{ node }">
                    <t-checkbox-group v-model="node.data.groupPermissionEnums" @change="(val, context) => handleChangeFlowPermission(node.data, val, context)" v-if="node.data?.groupPermissionEnums">
                      <t-checkbox label="查看" value="READ" :disabled="hasSameAuth(node.data.groupPermissionEnums, ['RUN', 'EDIT'])" ></t-checkbox>
                      <t-checkbox label="编辑" value="EDIT" :disabled="hasSameAuth(node.data.groupPermissionEnums, ['RUN'])"></t-checkbox>
                      <t-checkbox label="运行" value="RUN"></t-checkbox>
                      <t-checkbox label="授予" value="GRANT"></t-checkbox>
                    </t-checkbox-group>
                  </template>
                </authorize-tree>
              </t-tab-panel>
              <t-tab-panel value="git" label="Git项目">
                <authorize-tree :data="gitData" name="Git项目" @check="gitCheckAll" @reset="gitCheckReset">
                  <template #label="{ node }">
                    <div class="des-flex-align-center flex-wrap">
                      <des-icon name="des-micon-gitxiangmu" size="14" class="icon"/>
                      <span class="text">
                        <ellipsis-with-tooltip :text="node.data?.gitProjectName"></ellipsis-with-tooltip>
                      </span>
                    </div>
                  </template>
                  <template #operations="{ node }">
                    <t-checkbox-group v-model="node.data.groupPermissionEnums" v-if="node.data?.groupPermissionEnums">
                      <t-checkbox label="编辑" value="EDIT"></t-checkbox>
                      <t-checkbox label="使用" value="USE"></t-checkbox>
                      <t-checkbox label="授予" value="GRANT"></t-checkbox>
                    </t-checkbox-group>
                  </template>
                </authorize-tree>
              </t-tab-panel>
            </t-tabs>
          </template>
        </div>
      </t-form-item>
      <template v-if="formData.authAction === 'transferPermission'">
        <t-form-item label="转移对象" name="transferPersons">
          <t-checkbox-group v-model="formData.transferPersons">
            <t-checkbox key="user" label="用户" value="user" />
            <t-checkbox key="userGroup" label="用户组" value="userGroup" />
          </t-checkbox-group>
        </t-form-item>
        <t-form-item label="转移用户" name="user" v-if="formData.transferPersons.includes('user')">
          <t-select v-model="formData.user" style="width: 498px;"></t-select>
        </t-form-item>
        <t-form-item label="转移用户组" name="userGroup" v-if="formData.transferPersons.includes('userGroup')">
          <t-select v-model="formData.userGroup" style="width: 498px;"></t-select>
        </t-form-item>
        <t-form-item>
          <t-space :size="2" align="center" style="color: #FFB800;">
            <InfoCircleIcon size="16px"/>
            <span>权限转移，仅会在转移对象的原有权限基础上做权限扩充，不会覆盖对象原有权限</span>
          </t-space>
        </t-form-item>
      </template>
    </t-form>
  </BasicDialog>
</template>

<style lang="less" scoped>
.no-label-item {
  padding-left: 88px;
  .flex-wrap {
    width: 280px;
    gap: 4px;
    .icon {
      flex: 0 0 14px;
    }
    .text {
      flex: 1;
      overflow: hidden;
    }
  }
}
.t-tab-panel {
  padding-top: 12px;
}
</style>
