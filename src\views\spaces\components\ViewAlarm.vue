<script setup>
import { ref, defineExpose, reactive } from 'vue';
import BasicDialog from '@/components/BasicDialog.vue';
import BlockHeader from '@/components/BlockHeader.vue';
import EllipsisWithTooltip from '@/components/EllipsisWithTooltip.vue';
import { difference } from 'lodash';

const visible = ref(false);

const data = reactive({
  name: '',
  types: [],
  perType: 'WECHAT_STAFF',
  wechatRobotChatid: '',
  wechatReceivers: [],
  phoneReceivers: [],
  emailReceivers: [],
});

function staffNames(list = []) {
  return list.map(item => item.staffName).join('、');
}

function handleClose() {
  close();
}

function getTypes(data) {
  if (data.types.includes('WECHAT_STAFF') || data.types.includes('WECHAT_GROUP')) {
    // 排除WECHAT这个值，生产一个新数组
    const array = difference(data.types, ['WECHAT_STAFF', 'WECHAT_GROUP']);
    return [...array, 'WECHAT'];
  }
  return data.types;
}
function getPerType(data) {
  if (data.types.includes('WECHAT_STAFF')) {
    return 'WECHAT_STAFF';
  }
  if (data.types.includes('WECHAT_GROUP')) {
    return 'WECHAT_GROUP';
  }
  return 'WECHAT_STAFF';
}
function open(item = {}) {
  data.name = item.name;
  data.types = getTypes(item);
  data.perType = getPerType(item);
  data.wechatRobotChatid = item.wechatRobotChatid;
  data.wechatReceivers = item.wechatReceivers;
  data.phoneReceivers = item.phoneReceivers;
  data.emailReceivers = item.emailReceivers;
  visible.value = true;
}
function reset() {
  data.name = '';
  data.types = [];
  data.perType = 'WECHAT_STAFF';
  data.wechatRobotChatid = '';
  data.wechatReceivers = [];
  data.phoneReceivers = [];
  data.emailReceivers = [];
}
function close() {
  visible.value = false;
  reset();
}

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    width="604"
    :visible.sync="visible"
    :header="data.name"
    :showConfirmButton="false"
    @handleClose="handleClose"
  >
    <div class="des-space__view-alarm">
      <section v-if="data.types?.includes('WECHAT')">
        <block-header class="title" title="企微机器人告警配置" />
        <div class="item"><span class="label">人群类型</span><span>{{ data.perType === 1 ? '个人' : '群' }}</span></div>
        <div class="item" v-if="data.perType === 'WECHAT_STAFF'"><span class="label">接收人</span><ellipsis-with-tooltip :text="staffNames(data.wechatReceivers)"></ellipsis-with-tooltip></div>
        <div class="item" v-if="data.perType === 'WECHAT_GROUP'"><span class="label">chatid</span><ellipsis-with-tooltip :text="data.wechatRobotChatid"></ellipsis-with-tooltip></div>
      </section>
      <section v-if="data.types?.includes('PHONE')">
        <block-header class="title"  title="电话告警配置" />
        <div class="item"><span class="label">接收人</span><ellipsis-with-tooltip :text="staffNames(data.phoneReceivers)"></ellipsis-with-tooltip></div>
      </section>
      <section v-if="data.types?.includes('EMAIL')">
        <block-header class="title"  title="邮件告警配置" />
        <div class="item"><span class="label">接收人</span><ellipsis-with-tooltip :text="staffNames(data.emailReceivers)"></ellipsis-with-tooltip></div>
      </section>
    </div>
  </BasicDialog>
</template>

<style lang="less" scoped>
.des-space__view-alarm {
  section:not(:last-child) {
    margin-bottom: 33px;
  }
  .title {
    margin-bottom: 17px;
  }
  .item {
    display: flex;
    color: #333333;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    &:not(:last-child) {
      margin-bottom: 10px;
    }
    .label {
      width: 72px;
      flex-shrink: 0;
      color: #666666;
      text-align: right;
      margin-right: 8px;
    }
  }
}
</style>
