<script setup>
import { ref, reactive, defineExpose, defineProps, defineEmits, nextTick, computed } from 'vue';
import BasicDialog from '@/components/BasicDialog.vue';
import { showDialogAlert } from '@/utils/dialog.js';
import { useSpacesStore } from '@/stores/spaces';
import { difference } from 'lodash';

const props = defineProps({
  spaceId: {
    type: String,
    required: true,
  },
});

const visible = ref(false);
const formRef = ref(null);
const wechatReceiverRef = ref(null);
const phoneReceiverRef = ref(null);
const emailReceiverRef = ref(null);

const emit = defineEmits('update');

const spaceStore = useSpacesStore();

const formData = reactive({
  id: '',
  name: '',
  types: [],
  perType: 'WECHAT_STAFF',
  chatId: '',
  wechatReceiverIds: [],
  wechatReceivers: [],
  phoneReceiverIds: [],
  phoneReceivers: [],
  emailReceiverIds: [],
  emailReceivers: [],
});
const rawData = ref({});

const options = [
  { value: 'WECHAT', label: '企微机器人' },
  { value: 'PHONE', label: '电话' },
  { value: 'EMAIL', label: '邮件' },
];

const rules = {
  name: [{ required: true, message: '请输入名称' }, { validator: checkName, trigger: 'change' }],
  types: [{ required: true, message: '请选择告警类型' }],
  perType: [{ required: true, message: '请选择人群类型' }],
  chatId: [{ required: true, message: '请输入群聊id' }],
  wechatReceiverIds: [{ required: true, message: '请选择接收人' }],
  phoneReceiverIds: [{ required: true, message: '请选择接收人' }],
  emailReceiverIds: [{ required: true, message: '请选择接收人' }],
};

const disabled = computed(() => {
  const { name = '', types = [], perType, wechatReceiverIds = [], chatId = '', phoneReceiverIds = [], emailReceiverIds = [] } = formData;
  if (!name) return true;
  if (types.length === 0) return true;
  if (types.includes('WECHAT')) {
    if (!perType) return true;
    if (perType === 'WECHAT_STAFF' && wechatReceiverIds.length === 0) return true;
    if (perType === 'WECHAT_GROUP' && !chatId) return true;
  }
  if (types.includes('PHONE') && phoneReceiverIds.length === 0) return true;
  if (types.includes('EMAIL') && emailReceiverIds.length === 0) return true;
  return false;
});

async function checkName(val) {
  if (!val) {
    return {
      result: false,
      message: '名称不能为空',
      type: 'error',
    };
  }
  // 数据没变不校验重复
  if (rawData.value.name === val) {
    return true;
  }
  const { data } = await spaceStore.checkSpaceAlarmName({
    spaceId: props.spaceId,
    alertName: val,
  });
  if (data) {
    return {
      result: false,
      message: '名称已存在',
      type: 'error',
    };
  }
  return true;
}

function handleChangeStaff(filed, staffs) {
  formData[filed] = staffs.map(item => ({
    staffId: item.StaffID,
    engName: item.EngName,
    staffName: item.StaffName,
  }));
}

function getSubTypes() {
  if (formData.types.includes('WECHAT')) {
    // 排除WECHAT这个值，生产一个新数组
    const array = difference(formData.types, ['WECHAT']);
    return [...array, formData.perType];
  }
  return formData.types;
}

function getSubData() {
  const types = getSubTypes();
  const data = {
    id: formData.id,
    spaceId: props.spaceId,
    name: formData.name,
    types,
    chatId: formData.chatId,
    wechatReceivers: formData.wechatReceivers,
    phoneReceivers: formData.phoneReceivers,
    emailReceivers: formData.emailReceivers,
  };
  if (types.includes('WECHAT_STAFF')) {
    data.chatId = '';
  } else {
    data.wechatReceivers = [];
  }
  return data;
}

function staffDataToStaffData(list = []) {
  return list.map(item => ({
    StaffID: item.staffId,
    EngName: item.engName,
    StaffName: item.staffName,
  }));
}

function getTypes(data) {
  if (data.types.includes('WECHAT_STAFF') || data.types.includes('WECHAT_GROUP')) {
    // 排除WECHAT这个值，生产一个新数组
    const array = difference(data.types, ['WECHAT_STAFF', 'WECHAT_GROUP']);
    return [...array, 'WECHAT'];
  }
  return data.types;
}
function getPerType(data) {
  if (data.types.includes('WECHAT_STAFF')) {
    return 'WECHAT_STAFF';
  }
  if (data.types.includes('WECHAT_GROUP')) {
    return 'WECHAT_GROUP';
  }
  return 'WECHAT_STAFF';
}
function setFormData(data) {
  formData.id = data.id;
  formData.name = data.name;
  formData.types = getTypes(data);
  formData.perType = getPerType(data);
  formData.chatId = data.chatId;
  formData.wechatReceivers = data.wechatReceivers;
  formData.phoneReceivers = data.phoneReceivers;
  formData.emailReceivers = data.emailReceivers;
  formData.wechatReceiverIds = data.wechatReceivers.map(item => item.staffId);
  formData.phoneReceiverIds = data.phoneReceivers.map(item => item.staffId);
  formData.emailReceiverIds = data.emailReceivers.map(item => item.staffId);
  nextTick(() => {
    wechatReceiverRef.value?.setSelected(staffDataToStaffData(data.wechatReceivers));
    phoneReceiverRef.value?.setSelected(staffDataToStaffData(data.phoneReceivers));
    emailReceiverRef.value?.setSelected(staffDataToStaffData(data.emailReceivers));
  });
}

function handleChangePerType(val) {
  // 如果是编辑状态，切换的时候需要尝试设置人员选择选中值
  if (formData.id && val === 'WECHAT_STAFF') {
    nextTick(() => {
      wechatReceiverRef.value?.setSelected(staffDataToStaffData(formData.wechatReceivers));
    });
  }
}

function handleChangeTypes(val, context) {
  const { current, type } = context;
  if (type !== 'uncheck') {
    return;
  }
  if (current === 'WECHAT') {
    formData.wechatReceivers = [];
    formData.wechatReceiverIds = [];
    formData.chatId = '';
  } else if (current === 'PHONE') {
    formData.phoneReceivers = [];
    formData.phoneReceiverIds = [];
  } else {
    formData.emailReceivers = [];
    formData.emailReceiverIds = [];
  }
}

function reset() {
  formData.id = '';
  formData.name = '';
  formData.types = [];
  formData.perType = 'WECHAT_STAFF';
  formData.chatId = '';
  formData.wechatReceiverIds = [];
  formData.wechatReceivers = [];
  formData.phoneReceiverIds = [];
  formData.phoneReceivers = [];
  formData.emailReceiverIds = [];
  formData.emailReceivers = [];
}

async function handleTest() {
  const result = await formRef.value.validate();
  if (result === true) {
    await spaceStore.testAlarm(getSubData());
    showDialogAlert({
      title: '测试内容已发送，请同步验证是否成功',
    });
  }
}

function handleClose() {
  close();
}
async function handleConfirm() {
  const result = await formRef.value.validate();
  if (result === true) {
    const subData = getSubData();
    if (formData.id) {
      await spaceStore.updateAlarm(subData);
    } else {
      await spaceStore.addAlarm(subData);
    }
    close();
    emit('update');
  }
}

function open(data) {
  visible.value = true;
  if (data) {
    setFormData(data);
    rawData.value = data;
  }
}
function close() {
  visible.value = false;
  reset();
  rawData.value = {};
  formRef.value.reset();
}

defineExpose({ open, close });
</script>

<template>
  <BasicDialog
    class="des-space__put-alarm__dialog"
    width="560"
    :visible.sync="visible"
    :header="formData.id ? '修改告警' : '新增告警'"
    confirmButtonText="确定"
    :submitDisabled="disabled"
    @handleClose="handleClose"
    @handleConfirm="handleConfirm"
  >
    <t-form class="des-space__put-alarm" :data="formData" style="padding-right:12px;" ref="formRef" labelWidth="78px" :rules="rules">
      <t-form-item label="告警名称" name="name">
        <t-input v-model="formData.name" placeholder="请输入" :maxlength="30" show-limit-number></t-input>
      </t-form-item>
      <t-form-item label="告警类型" name="types">
        <t-checkbox-group v-model="formData.types" :options="options" @change="handleChangeTypes" />
      </t-form-item>
      <section v-if="formData.types.length" class="des-space__put-alarm__type-info-container">
        <template v-if="formData.types.includes('WECHAT')">
          <div class="des-space__put-alarm__type-info-item">
            <div class="title">企微机器人告警配置</div>
            <div class="content">
              <t-form-item label="人群类型" name="perType">
                <t-radio-group v-model="formData.perType" @change="handleChangePerType">
                  <t-radio :value="'WECHAT_STAFF'">个人</t-radio>
                  <t-radio :value="'WECHAT_GROUP'">群</t-radio>
                </t-radio-group>
              </t-form-item>
              <t-form-item v-if="formData.perType === 'WECHAT_STAFF'" label="接收人" name="wechatReceiverIds">
                <sdc-staff-selector v-model="formData.wechatReceiverIds" ref="wechatReceiverRef" multiple style="width:100%;" placeholder="请选择人员" modalClass="des-sdc-modal--fix" size="small" @change="(choosed) => handleChangeStaff('wechatReceivers', choosed)"></sdc-staff-selector>
              </t-form-item>
              <t-form-item v-if="formData.perType === 'WECHAT_GROUP'" label="chatid" name="chatId" help="注意：该告警配置需先拉“HR中台业务”机器人进群">
                <t-input v-model="formData.chatId" placheholder="请输入"></t-input>
              </t-form-item>
            </div>
          </div>
        </template>
        <template v-if="formData.types.includes('PHONE')">
          <div class="des-space__put-alarm__type-info-item">
            <div class="title">电话告警配置</div>
            <div class="content">
              <t-form-item label="接收人" name="phoneReceiverIds">
                <sdc-staff-selector v-model="formData.phoneReceiverIds" ref="phoneReceiverRef" multiple style="width:100%;" placeholder="请选择人员" modalClass="des-sdc-modal--fix" size="small" @change="(choosed) => handleChangeStaff('phoneReceivers', choosed)"></sdc-staff-selector>
              </t-form-item>
            </div>
          </div>
        </template>
        <template v-if="formData.types.includes('EMAIL')">
          <div class="des-space__put-alarm__type-info-item">
            <div class="title">邮件告警配置</div>
            <div class="content">
              <t-form-item label="接收人" name="emailReceiverIds">
                <sdc-staff-selector v-model="formData.emailReceiverIds" ref="emailReceiverRef" multiple style="width:100%;" placeholder="请选择人员" modalClass="des-sdc-modal--fix" size="small" @change="(choosed) => handleChangeStaff('emailReceivers', choosed)"></sdc-staff-selector>
              </t-form-item>
            </div>
          </div>
        </template>
      </section>
    </t-form>
    <template #footer-left v-if="formData.types.length">
      <t-button :disabled="disabled" theme="default" variant="outline" @click="handleTest" style="margin-right:8px;">测试</t-button>
    </template>
  </BasicDialog>
</template>

<style lang="less" scoped>
.des-space__put-alarm {
  :deep(.t-form__label--right) {
    padding-right: 12px;
  }
}
.des-space__put-alarm__dialog {
  :deep(.footer-wrap) {
    justify-content: end;
  }
}
.des-space__put-alarm__type-info-container {
  display: flex;
  padding: 16px;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
  align-self: stretch;
  border-radius: 4px;
  background: #EBF0F7;
}
.des-space__put-alarm__type-info-item {
  width: 100%;
  border-radius: 4px;
  background: #FFF;
  .title {
    padding: 8px 12px;
    border-bottom: 1px solid #EEE;
    color: #333333;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
  }
  .content {
    padding: 16px 12px;
  }
}
</style>
