/** 查看日志弹窗 */
<template>
  <BasicDialog
    width="1240"
    :visible.sync="visible"
    header="查看日志"
    :mode="modalType"
    :footer="false"
    :showFooter="false"
    placement="center"
  >
    <div class="tool-wrap">
      <div class="des-flex-align-center icon-button-wrap"  @click="handleRefresh">
        <des-icon name="des-icon-shuaxin" size="16" class="icon"></des-icon>
        <t-link theme="primary" hover="color">
          刷新
        </t-link>
      </div>
      <div class="des-flex-align-center icon-button-wrap" @click="handleFullScreen">
        <des-icon :name="iconName" size="16" class="icon"></des-icon>
        <t-link theme="primary" hover="color">
          {{ fullScreen ? '退出全屏' : '全屏' }}
        </t-link>
      </div>
    </div>
    <div class="content-wrap" :class="{ 'full-screen': fullScreen }">
      <p v-for="(item, index) in content" :key="index">{{ item }}</p>
    </div>
    <t-radio-group v-model="logType" @change="handleRefresh">
      <t-radio value="task">执行日志</t-radio>
      <t-radio value="schedule">调度日志</t-radio>
    </t-radio-group>
  </BasicDialog>
</template>

<script setup>
import { computed, defineExpose, ref, defineProps } from 'vue';
import BasicDialog from '@/components/BasicDialog.vue';
import { useRoute } from 'vue-router/composables';

const props = defineProps({
  getScheduleApi: { // 调度日志接口
    type: Function,
    required: true,
  },
  getExecuteApi: { // 执行日志接口
    type: Function,
    required: true,
  },
});

const route = useRoute();

const logType = ref('task');

const modalType = ref('modal');
const fullScreen = computed(() => modalType.value === 'full-screen');
const iconName = computed(() => (fullScreen.value ? 'des-icon-tuichuquanping' : 'des-icon-quanping'));
const handleFullScreen = () => {
  modalType.value = fullScreen.value ? 'modal' : 'full-screen';
};

// 弹窗数据
const visible = ref(false);
// 当前数据
let currentRow = {};

const open = (row) => {
  currentRow = row;
  initData(row);
  visible.value = true;
};

// 显示的日志
const content = ref('');
const initData = async (row) => {
  const api = logType.value === 'schedule'
    ? props.getScheduleApi
    : props.getExecuteApi;
  const data = await api(row, route.query.spaceId);
  content.value = data?.split('\n') || [];
};
const handleRefresh = () => {
  initData(currentRow);
};

defineExpose({ open });
</script>

<style lang="less" scoped>
.tool-wrap {
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--td-brand-color);
  margin-bottom: 8px;
  .icon-button-wrap {
    cursor: pointer;
    .icon {
      margin-right: 4px;
    }
  }
}
.content-wrap {
  background: #F2F2F2;
  border-radius: 2px;
  padding: 12px 16px;
  height: 434px;
  overflow-y: auto;
  margin-bottom: 20px;
}
.full-screen {
  height: 85vh;
}
</style>
