<script setup>
import { ref, reactive, computed, defineProps, onMounted } from 'vue';
import { AddIcon } from 'tdesign-icons-vue';
import { MessagePlugin } from 'tdesign-vue';
import dayjs from 'dayjs';
import UserName from '@/components/UserName.vue';
import BasicSearch from '@/components/BasicSearch.vue';
import PutAlarm from './PutAlarm.vue';
import ViewAlarm from './ViewAlarm.vue';
import { useSearch } from '@/hooks';
import { useSpacesStore } from '@/stores/spaces';
import { showDialogConfirm } from '@/utils/dialog';
import NoData from '@/components/NoData.vue';

const props = defineProps({
  spaceId: {
    type: String,
    default: '',
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  showButton: {
    type: Boolean,
    default: false,
  },
});
const loading = ref(false);

const spaceStore = useSpacesStore();

const alarmPagination = reactive({
  current: 1,
  pageSize: 10,
  showJumper: true,
  onChange: (pageInfo) => {
    alarmPagination.current = pageInfo.current;
    if (alarmPagination.pageSize !== pageInfo.pageSize) {
      alarmPagination.page = 1;
    }
    alarmPagination.pageSize = pageInfo.pageSize;
    fetchData();
  },
});
const alarmSearchData = reactive({
  queryKey: '',
  alertType: null,
});
// 告警管理
const alarmData = ref([]);

async function fetchData() {
  loading.value = true;
  try {
    const { data } = await spaceStore.getAlarmList({
      queryData: {
        queryKey: alarmSearchData.queryKey,
        spaceId: props.spaceId,
        alertType: alarmSearchData.alertType,
      },
      pageNum: alarmPagination.current,
      pageSize: alarmPagination.pageSize,
    });
    alarmData.value = data.list;
    alarmPagination.total = data.total;
  } catch (e) {
  }
  loading.value = false;
}
const { onSearch } = useSearch(fetchData);

function handleReset() {
  alarmSearchData.queryKey = '';
  alarmSearchData.alertType = null;
  fetchData();
}

function typesToName(types) {
  const map = {
    WECHAT_STAFF: '企业微信',
    WECHAT_GROUP: '企业微信',
    PHONE: '电话',
    EMAIL: '邮件',
  };
  return types.map(item => map[item] || item).join('、');
}

onMounted(() => {
  fetchData();
});
const alarmColumns = computed(() => {
  const list = [
    { colKey: 'serial-number', width: 61, title: '序号' },
    {
      colKey: 'name',
      title: '告警名称',
      cell: (h, { row }) => (<t-link hover="color" theme="primary" onClick={() => handleViewAlarm(row)}>{row.name}</t-link>),
    },
    {
      colKey: 'types',
      title: '告警类型',
      cell: (h, { row }) => (<span>{typesToName(row.types)}</span>),
    },
    {
      colKey: 'createBy',
      title: '创建人',
      cell: (h, { row }) => (
        <UserName fullName={row.createUser}></UserName>
      ),
    },
    { colKey: 'createTime', title: '创建时间', cell: (h, { row }) => (<span>{dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')}</span>) },
  ];
  if (!props.readonly) {
    list.push({
      colKey: 'operation',
      title: '操作',
      width: 110,
      cell: (h, { row }) => (
        <t-space size="16px">
          <t-link hover="color" theme="primary" onClick={() => handleEditAlarm(row)}>编辑</t-link>
          <t-link hover="color" theme="primary" onClick={() => handleRemoveAlarm(row)}>删除</t-link>
        </t-space>
      ),
    });
  }
  return list;
});
const putAlarmRef = ref(null);
function handleAddAlarm() {
  putAlarmRef.value.open();
}
async function handleEditAlarm(row) {
  const { data } = await spaceStore.getAlarmDetail(row.id);
  putAlarmRef.value.open(data);
}
const viewAlarmRef = ref(null);
async function handleViewAlarm(row) {
  const { data } = await spaceStore.getAlarmDetail(row.id);
  viewAlarmRef.value.open(data);
}
async function handleRemoveAlarm(row) {
  const result = await showDialogConfirm({
    title: `确定删除告警 ${row.name} 吗？`,
    body: '删除后，无法恢复。',
  });
  if (result.type === 'cancel') return;
  await spaceStore.deleteAlarm(row.id);
  MessagePlugin.success('删除成功');
  if (alarmData.value.length === 1 && alarmPagination.current > 1) {
    alarmPagination.current -= 1;
  }
  fetchData();
}
</script>

<template>
  <div class="block-alarm">
    <div class="title">
      <h2>告警管理</h2>
    </div>
    <div class="search-content">
      <basic-search v-model="alarmSearchData.queryKey" @reset="handleReset" @change="onSearch">
        <template #prefix>
          <t-select v-model="alarmSearchData.alertType" @change="onSearch" :options="[{label: '全部告警类型', value: null }, { label: '企微机器人', value: 1 }, { label: '电话', value: 2 }, { label: '邮件', value: 3 }]"></t-select>
        </template>
      </basic-search>
      <div class="add-alarm-btn-group" v-if="!readonly && showButton">
        <t-button theme="primary" @click="handleAddAlarm"><add-icon slot="icon" />新增告警</t-button>
      </div>
    </div>
    <div class="page-space-detail__alarm-list">
      <t-table row-key="id" :data="alarmData" :columns="alarmColumns" :pagination="alarmPagination" :loading="loading">
        <template #empty>
          <no-data text="暂无数据"></no-data>
        </template>
      </t-table>
    </div>
    <!-- 新增/修改告警 -->
    <put-alarm ref="putAlarmRef" :spaceId="spaceId" @update="fetchData"></put-alarm>
    <!-- 查看告警 -->
    <view-alarm ref="viewAlarmRef" :spaceId="spaceId"></view-alarm>
  </div>
</template>

<style lang="less" scoped>
.block-alarm {
  background-color: #fff;
  border-radius: 4px;
  .title {
    display: flex;
    height: 56px;
    padding: 0 20px;
    align-items: center;
    justify-content: space-between;
  }
  h2 {
    color: #333333;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
  }
  .search-content {
    display: flex;
    padding: 0 20px;
    margin-bottom: 16px;
  }
  .add-alarm-btn-group {
    flex-grow: 1;
    text-align: right;
  }
}
</style>
