// ------------------工作流定义-------------------
// 工作流定义列表
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_LIST = spaceId => `/api/spaces/${spaceId}/processes/list`;
// 保存工作流草稿
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_SAVE_DRAFT = (spaceId, code) => `/api/spaces/${spaceId}/processes/${code}/draft`;
// 新建工作流
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_CREATE = spaceId => `/api/spaces/${spaceId}/processes/create`;
// 更新/保存工作流(put)
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_UPDATE = (spaceId, code) => `/api/spaces/${spaceId}/processes/${code}/update`;
// 删除工作流
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_DELETE = (spaceId, code) => `/api/spaces/${spaceId}/processes/${code}`;
// 运行工作流(post)
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_RUN = (spaceId, code) => `/api/spaces/${spaceId}/processes/${code}/run`;
// 发布工作流(post)
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_PUBLISH = (spaceId, code) => `/api/spaces/${spaceId}/processes/${code}/publish`;
// 批量发布工作流(post)
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_BATCH_PUBLISH = spaceId => `/api/spaces/${spaceId}/processes/batchPublish`;
// 下线工作流(post)
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_OFFLINE = (spaceId, code) => `/api/spaces/${spaceId}/processes/${code}/offline`;
// 批量下线工作流(post)
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_BATCH_OFFLINE = spaceId => `/api/spaces/${spaceId}/processes/batchOffline`;
// 工作流详情
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_DETAIL = (spaceId, code) => `/api/spaces/${spaceId}/processes/${code}`;
// 根据工作流name获取工作流详情
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_DETAIL_BY_NAME = spaceId => `/api/spaces/${spaceId}/processes/getByName`;
// 工作流任务节点详情
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_TASK_DETAIL = (spaceId, code) => `/api/spaces/${spaceId}/processes/${code}/tasks`;
// 工作流版本切换
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_VERSION_SWITCH = () => '/api/processVersions/switch';
// 工作流版本查询
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_VERSION_LIST = () => '/api/processVersions/list';
// 工作流版本(查询 删除)
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_VERSION = id => `/api/processVersions/${id}`;
// 查询授权成员信息
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_AUTH_GRANTORS = (spaceId, code) => `/api/spaces/${spaceId}/processes/${code}/grantors`;
// 授权
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_AUTH_GRANT = (spaceId, code) => `/api/spaces/${spaceId}/processes/${code}/grant`;
// 批量授权
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_AUTH_BATCH_GRANT = spaceId => `/api/spaces/${spaceId}/processes/batchGrant`;
// 授权移除
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_AUTH_REVOKE = (spaceId, code) => `/api/spaces/${spaceId}/processes/${code}/revoke`;
// 工作流补录
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_SUPPLEMENT = (spaceId, code) => `/api/spaces/${spaceId}/processes/${code}/rerun`;
// 工作流加锁
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_LOCK = (spaceId, code) => `/api/spaces/${spaceId}/processes/${code}/lock`;
// 工作流解锁
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_UNLOCK = (spaceId, code) => `/api/spaces/${spaceId}/processes/${code}/unlock`;
// 工作流实例获取所有BIDS的App
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_BIDS_APPS = () => '/api/bids/getApps';
// 工作流实例根据任务编码获取任务列表
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_GET_TASK_BY_CODE = () => '/api/bids/getTasksByCode';
// 导入工作流
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_IMPORT = spaceId => `/api/spaces/${spaceId}/processes/import`;
// 获取批量发布结果
export const API_DATA_DEVELOPMENT_FLOW_DEFINE_BATCH_PUBLISH_RESULT = spaceId => `/api/spaces/${spaceId}/processes/getPublishResult`;
// 工作流导航树(搜索)
export const API_DATA_DEVELOPMENT_FLOW_TREE = spaceId => `/api/spaces/${spaceId}/processes/tree`;
// 工作流导航树(第一层)
export const API_DATA_DEVELOPMENT_FLOW_TREE_PROCESS = spaceId => `/api/spaces/${spaceId}/processes/processTree`;
// 工作流导航树(第二层)
export const API_DATA_DEVELOPMENT_FLOW_TREE_TASK = (spaceId, code) => `/api/spaces/${spaceId}/processes/processChildTree/${code}`;

// ------------------工作流定时调度管理-------------------
// 定时调度上线
export const API_DATA_DEVELOPMENT_FLOW_TIMER_ONLINE = id => `/api/schedules/${id}/online`;
// 定时调度下线
export const API_DATA_DEVELOPMENT_FLOW_TIMER_OFFLINE = id => `/api/schedules/${id}/offline`;
// 查询定时调度明细（get）/新增(post)/更新(post)
export const API_DATA_DEVELOPMENT_FLOW_TIMER_GENERAL = () => '/api/schedules';
// 定时调度删除
export const API_DATA_DEVELOPMENT_FLOW_TIMER_DELETE = id => `/api/schedules/${id}`;


// ------------------工作流实例------------------
// 工作流实例列表
export const API_DATA_DEVELOPMENT_FLOW_INSTANCE_LIST = () => '/api/processInstance/pageList';
// 工作流实例调度日志
export const API_DATA_DEVELOPMENT_FLOW_INSTANCE_LOG = () => '/api/processInstance/jobLog';
// 工作流实例执行日志
export const API_DATA_DEVELOPMENT_FLOW_INSTANCE_POD_LOG = () => '/api/processInstance/jobPodLog';
// 工作流实例操作
export const API_DATA_DEVELOPMENT_FLOW_INSTANCE_OPERATE = () => '/api/processInstance/jobOperate';
// 工作流实例标识跳转工作流实例详情
export const API_DATA_DEVELOPMENT_FLOW_INSTANCE_DETAIL = (spaceId, code, processInstanceId) => `/api/spaces/${spaceId}/processes/${code}/getWithInstance/${processInstanceId}`;
// 查询工作流实例ID（用于工作流实例详情中 跳转到 子节点的实例）
export const API_DATA_DEVELOPMENT_FLOW_INSTANCE_SUB_INSTANCE_INFO = () => '/api/processInstance/querySubInstanceInfo';

// ------------------任务实例------------------
// 任务实例列表
export const API_DATA_DEVELOPMENT_TASK_INSTANCE_LIST = () => '/api/taskInstance/pageList';
// 任务实例调度日志
export const API_DATA_DEVELOPMENT_TASK_INSTANCE_LOG = () => '/api/taskInstance/taskLog';
// 任务实例执行日志
export const API_DATA_DEVELOPMENT_TASK_INSTANCE_POD_LOG = () => '/api/taskInstance/taskPodLog';
// 任务实例操作
export const API_DATA_DEVELOPMENT_TASK_INSTANCE_OPERATE = () => '/api/taskInstance/taskOperate';

// ------------------GIT项目管理------------------
// Git项目管理列表
export const API_DATA_DEVELOPMENT_GIT_MANAGER_LIST = () => '/api/gitProjectManager/list';
// Git项目（新增/修改/删除/详情，除新增外需要增加id作为参数）
export const API_DATA_DEVELOPMENT_GIT_MANAGER_OPERATE = (id = '') => (id ? `/api/gitProjectManager/${id}` : '/api/gitProjectManager');
// Git项目获取分支
export const API_DATA_DEVELOPMENT_GIT_MANAGER_BRANCHES = () => '/api/gitProjectManager/branches';
// 判断当前空间下Git项目名是否重复
export const API_DATA_DEVELOPMENT_GIT_MANAGER_CHECK_NAME = () => '/api/gitProjectManager/checkProjectName';
// Git项目构建
export const API_DATA_DEVELOPMENT_GIT_MANAGER_BUILD = () => '/api/gitProjectManager/build';
// 查询Git项目是否已经绑定工作流
export const API_DATA_DEVELOPMENT_GIT_MANAGER_CHECK = () => '/api/gitProjectManager/checkIsBoundWorkFlow';
// 查询已授权列表
export const API_DATA_DEVELOPMENT_GIT_MANAGER_GRANTORS = (spaceId, gitManagerId) => `/api/gitProjectManager/${spaceId}/grantors/${gitManagerId}`;
// 授权
export const API_DATA_DEVELOPMENT_GIT_MANAGER_GRANT = (spaceId, gitManagerId) => `/api/gitProjectManager/${spaceId}/${gitManagerId}/grant`;
// 授权移除
export const API_DATA_DEVELOPMENT_GIT_MANAGER_REVOKE = () => '/api/gitProjectManager/revoke';
// 查询项目下的文件路径
export const API_DATA_DEVELOPMENT_GIT_MANAGER_FILE_PATH = () => '/api/gitProjectManager/list_files';
// 查询文件内容
export const API_DATA_DEVELOPMENT_GIT_MANAGER_FILE_CONTENT = () => '/api/gitProjectManager/get_file_content';

// 调试任务节点
export const API_DATA_DEVELOPMENT_FLOW_DEBUG = ({ spaceId, processCode, taskCode }) => `/api/spaces/${spaceId}/processes/${processCode}/tasks/${taskCode}/run`;
// 停止调试任务节点
export const API_DATA_DEVELOPMENT_FLOW_DEBUG_STOP = ({ spaceId, processCode, jobName }) => `/api/spaces/${spaceId}/processes/${processCode}/tasks/${jobName}/stop`;
// 查看调试结果列表
export const API_DATA_DEVELOPMENT_FLOW_DEBUG_LIST = ({ spaceId, processCode }) => `/api/spaces/${spaceId}/processes/${processCode}/tasks/responseList`;
// 任务调试日志
export const API_DATA_DEVELOPMENT_FLOW_DEBUG_LOG = ({ spaceId, processCode, jobName }) => `/api/spaces/${spaceId}/processes/${processCode}/tasks/${jobName}/logs`;
