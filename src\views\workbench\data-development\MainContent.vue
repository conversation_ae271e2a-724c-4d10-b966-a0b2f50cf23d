<script setup>
import { defineAsyncComponent, watch } from 'vue';
import emitter, { EVENT_NAMES } from '@/utils/emitter';
import { storeToRefs } from 'pinia';
import { useDataDevelopmentStore } from '@/stores/data-development';
import { showDialogConfirm } from '@/utils/dialog';

const DataDevelopment = defineAsyncComponent(() => import('./main-tabs/DataDevelopment.vue'));
const WorkFlowPanel = defineAsyncComponent(() => import('./main-tabs/work-flow-panel/MainPage.vue'));
const GitProjectPanel = defineAsyncComponent(() => import('./main-tabs/GitProjectPanel.vue'));
const FlowDefinePanel = defineAsyncComponent(() => import('./main-tabs/FlowDefinePanel.vue'));

const dataDevelopmentStore = useDataDevelopmentStore();
const { currentTabValue, tabPanelList } = storeToRefs(dataDevelopmentStore);

async function removeTab(item, options = { showConfirm: true }) {
  const { childPanel, closeReminder, needCompareNodeData } = dataDevelopmentStore.currentTabPanel;
  if (!options.showConfirm || !(closeReminder)) {
    dataDevelopmentStore.removeTabPanelList(item.value, childPanel);
    return;
  }

  if (needCompareNodeData) {
    emitter.emit(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_DATA_COMPARE, { panel: currentTabValue.value });
    if (dataDevelopmentStore.flowDataIsEqual) {
      dataDevelopmentStore.removeTabPanelList(item.value, childPanel);
      return;
    }
  }

  const { type } = await showDialogConfirm({
    title: '确定退出配置吗？',
    body: '退出后，您编辑的内容将不会保存',
    width: '440px',
  });

  if (type === 'success') {
    dataDevelopmentStore.removeTabPanelList(item.value, childPanel);
  }
}
function changeTab(val) {
  dataDevelopmentStore.setCurrentTabValue(val);
}
// 组件映射
function getComp(name) {
  let comp = null;
  switch (name) {
    case 'DataDevelopment':
      comp = DataDevelopment;
      break;
    case 'WorkFlowPanel':
      comp = WorkFlowPanel;
      break;
    case 'GitProjectPanel':
      comp = GitProjectPanel;
      break;
    case 'FlowDefinePanel':
      comp = FlowDefinePanel;
      break;
    default:
      break;
  }
  return comp;
}
// 属性保存对象，用于子组件触发tab切换
const compPorpsMap = {
  DataDevelopment: {},
  WorkFlowPanel: {},
  GitProjectPanel: {},
  FlowDefinePanel: {},
};
function switchTab(tabName = '', props = {}) {
  if (tabName) {
    compPorpsMap[tabName] = props;
    changeTab(tabName);
  }
}

const refMap = {};
function setRefMap(el, current) {
  if (el) {
    refMap[current] = el;
  }
}

watch(currentTabValue, (val) => {
  if (val === 'DataDevelopment') {
    const panel = refMap.DataDevelopment;
    panel.fetchPanelData();
  }
});
</script>

<template>
  <div class="page-data-dev__main-content">
    <t-tabs
      :value="currentTabValue"
      theme="card"
      @remove="removeTab"
      @change="changeTab"
      style="width: 100%;height:100%;"
    >
      <t-tab-panel
        v-for="data in tabPanelList"
        class="page-data-dev__main-content__tab"
        :key="data.value"
        :value="data.value"
        :label="data.label"
        :removable="data.removable"
        :destroyOnHide="false"
      >
        <component :is="getComp(data.component)" :ref="(el) => setRefMap(el, data.value)" :panel="data.value" @switch="switchTab" @remove="removeTab" v-bind="{ ...data.props, ...compPorpsMap[data.value] }" />
      </t-tab-panel>
    </t-tabs>
  </div>
</template>

<style lang="less" scoped>
.page-data-dev__main-content {
  height: 100%;
  :deep(.t-tabs__nav--card) {
    border-radius: 4px 4px 0 0;
  }
  & > :deep(.t-tabs > .t-tabs__header ) {
    .t-tabs__nav-item {
      height: 38px;
      line-height: 38px;
    }
    .t-tabs__nav--card.t-tabs__nav-item.t-is-active {
      color: #2A2A2A;
    }
  }
  & > :deep(.t-tabs > .t-tabs__header > .t-tabs__nav) {
    .t-tabs__nav-scroll {
      background: #EBF0F7
    };
  }
  :deep(.t-tabs__content) {
    height: calc(100% - 38px);
  }
  .page-data-dev__main-content__tab {
    height: 100%;
  }
}
</style>
