<template>
  <div style="height: 100%">
    <PageLayout>
      <template #search>
        <BasicSearch v-model="searchKey" placeholder="名称/创建人..." @reset="handleReset" @change="searchChange">
          <template #prefix>
            <t-select
              v-model="datasourceId"
              :options="datasourceOptions"
              placeholder="数据源"
              filterable
              @change="searchChange"
            ></t-select>
          </template>
        </BasicSearch>
      </template>

      <template #button>
        <t-button theme="primary" @click="handleAdd"><add-icon slot="icon" />新增资源组</t-button>
      </template>

      <template #table>
        <t-table rowKey="name" :data="tableData" :columns="columns" :pagination="pagination" max-height="68vh">
          <template #empty v-if="!datasourceId">
            <no-data text="请先选择数据源"></no-data>
          </template>
        </t-table>
      </template>
    </PageLayout>
    <SRResourceDialog ref="dialogRef" :datasourceOptions="datasourceOptions" @fetchData="fetchData"></SRResourceDialog>
    <ResourceUseDialog ref="resourceUseDialogRef"></ResourceUseDialog>
  </div>
</template>

<script setup lang="jsx">
import { to } from '@/utils/util';
import { ref, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue';
import { AddIcon } from 'tdesign-icons-vue';
import { useSearch, usePagination } from '@/hooks';
import { showDialogConfirm } from '@/utils/dialog';
import { useOperationsStore } from '@/stores/operations';
import { PageLayout } from '@/views/operations/components';
import SRResourceDialog from './SRResourceDialog.vue';
import BasicSearch from '@/components/BasicSearch.vue';
import NoData from '@/components/NoData.vue';
import ResourceUseDialog from './ResourceUseDialog.vue';

const operationsStore = useOperationsStore();

const datasourceId = ref('');
const datasourceOptions = ref([]);

// 表格列
const columns = ref([
  { colKey: 'serial-number', width: 61, title: '序号', fixed: 'left' },
  {
    colKey: 'name',
    title: '资源组名称',
    width: 118,
    ellipsis: true,
  },
  {
    colKey: 'sourceSql',
    title: '资源组SQL',
    width: 839,
    ellipsis: true,
  },
  {
    title: '操作',
    colKey: 'operate',
    width: 230,
    fixed: 'right',
    cell: (h, { row }) => (
      <t-space>
        <t-link theme="primary" hover="color" onClick={ () => handleEdit(row)}>编辑</t-link>
        <t-link theme="primary" hover="color" onClick={ () => handleUse(row)}>资源使用情况</t-link>
        <t-link theme="primary" hover="color" onClick={ () => handleDel(row) }>删除</t-link>
      </t-space>
    ),
  },
]);

const tableData = ref([]);

const fetchData = async () => {
  if (!datasourceId.value) return;
  const { current: pageNum, pageSize } = pagination;
  const params = {
    pageNum,
    pageSize,
    queryData: { queryKey: searchKey.value },
  };
  const [err, data] = await to(operationsStore.fetchSourceGroupList(datasourceId.value, params));
  if (err) {
    return;
  }
  const { total, list } = data;
  setPaginationTotal(total);
  tableData.value = list || [];
};

const { searchKey, setSearchKey, onSearch } = useSearch(fetchData);
const { pagination, setPaginationCurrent, setPaginationTotal } = usePagination(fetchData);

const searchChange = () => {
  setPaginationCurrent(1);
  onSearch();
};

// 重置按钮
const handleReset = () => {
  setSearchKey('');
  setPaginationCurrent(1);
  fetchData();
};

const handleEdit = (row) => {
  dialogRef.value.openEditDialog({ ...row, dataSourceId: datasourceId.value });
};

const handleDel = async (row) => {
  const { type } = await showDialogConfirm({
    title: `确定删除资源组「${row.name}」吗？`,
    body: '删除后不可恢复',
    width: '440px',
  });
  if (type === 'cancel') return;
  const [err, data] = await to(operationsStore.fetchSourceGroupDel(datasourceId.value, row.name));
  if (err) return;
  if (data) {
    fetchData();
    MessagePlugin('success', '操作成功');
  }
};

const resourceUseDialogRef = ref(null);
const handleUse = (row) => {
  resourceUseDialogRef.value.show(row.userInfoList || []);
};

const dialogRef = ref(null);
const handleAdd = () => {
  dialogRef.value.openAddDialog();
};

const getDataSourceOptions = async () => {
  const data = { pageNum: 1, pageSize: 100, queryData: { queryKey: '' } };
  const [err, result] = await to(operationsStore.fetchDatasourcesList(data));
  if (err) {
    return;
  }
  datasourceOptions.value = result.list.map(item => ({
    ...item,
    label: item.name,
    value: item.id,
  }));
};

onMounted(() => {
  getDataSourceOptions();
});
</script>

<style lang="less" scoped></style>
