<template>
  <BasicDialog
    width="560"
    :visible.sync="visible"
    :submitDisabled="submitDisabled"
    header="编辑参数"
    :onClosed="onClose"
    @handleClose="onClose"
    @handleConfirm="onConfirm"
  >
    <t-form ref="formRef" :data="form" :rules="rules" label-width="143px">
      <t-form-item label="所属SR数据源" name="datasourceId">
        <t-select v-model="form.datasourceId" :options="datasourceOptions" placeholder="请选择" disabled/>
      </t-form-item>
      <t-form-item label="参数名" name="variableName">
        <t-input v-model="form.variableName" placeholder="请输入"/>
      </t-form-item>
      <t-form-item label="参数值" name="value">
        <t-input v-model="form.value" placeholder="请输入"/>
      </t-form-item>
    </t-form>
  </BasicDialog>
</template>

<script setup>
import { computed, defineExpose, defineProps, defineEmits } from 'vue';
import BasicDialog from '@/components/BasicDialog.vue';
import { useDialog } from '@/views/operations/hooks';
import { useForm } from '@/hooks';
import { useOperationsStore } from '@/stores/operations';
import { MessagePlugin } from 'tdesign-vue';
import { to } from '@/utils/util';

const emit = defineEmits(['fetchData']);

defineProps({
  datasourceOptions: {
    type: Array,
    default: () => [],
  },
});

const operationsStore = useOperationsStore();
const initFormData = {
  datasourceId: '',
  variableName: '',
  value: '',
};
const initFormRules = {
  variableName: [
    {
      required: true,
      message: '必填',
      type: 'error',
      trigger: 'change',
    },
    {
      whitespace: true,
      message: '不能为空',
    },
  ],
  value: [
    {
      required: true,
      message: '必填',
      type: 'error',
      trigger: 'change',
    },
    {
      whitespace: true,
      message: '不能为空',
    },
  ],
};

const editCb = (data) => {
  Object.assign(form, data);
};

const closeCb = () => {
  resetForm();
};

const confirmCb = async () => {
  const valid = await validateForm();
  if (!valid) return;
  const params = {
    key: form.variableName,
    value: form.value,
    datasourceId: form.datasourceId,
  };
  const [err] = await to(operationsStore.fetchDynamicParamSet(form.datasourceId, params));
  if (err) {
    return;
  }
  emit('fetchData');
  MessagePlugin('success', '操作成功');
  return true;
};

// 表单数据
const { form, rules, formRef, validateForm, resetForm } = useForm(
  initFormData,
  initFormRules,
);

// 弹窗数据
const { visible, openAddDialog, openEditDialog, onClose, onConfirm } = useDialog({
  editCb,
  confirmCb,
  closeCb,
});

// 提交按钮禁用状态
const submitDisabled = computed(() => !(form.datasourceId && form.variableName && form.value));

defineExpose({ openAddDialog, openEditDialog });
</script>

<style lang="less" scoped>

</style>
