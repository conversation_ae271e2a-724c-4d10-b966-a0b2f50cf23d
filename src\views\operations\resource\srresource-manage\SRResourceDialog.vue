<template>
  <BasicDialog
    width="560"
    :visible.sync="visible"
    :header="header"
    :submitDisabled="submitDisabled"
    :onClosed="onClose"
    @handleClose="onClose"
    @handleConfirm="onConfirm"
  >
    <t-form ref="formRef" :data="form" :rules="rules" label-width="127px">
      <t-form-item label="SR数据源" name="dataSourceId">
        <t-select v-model="form.dataSourceId" :options="datasourceOptions" :disabled="isEditType" filterable placeholder="请选择" @change="handleChangeDatasource"/>
      </t-form-item>
      <t-form-item label="资源组名称" name="name">
        <t-input v-model="form.name" placeholder="请输入" :disabled="!form.dataSourceId" :maxlength="30" show-limit-number />
      </t-form-item>
    </t-form>
    <div class="resource-wrap" v-if="visible">
      <BasicSqlEditor v-model="form.sourceSql" title="资源组 SQL"></BasicSqlEditor>
    </div>
  </BasicDialog>
</template>

<script setup>
import BasicDialog from '@/components/BasicDialog.vue';
import { defineExpose, computed, defineProps, defineEmits } from 'vue';
import { useDialog } from '@/views/operations/hooks';
import { useForm } from '@/hooks';
import BasicSqlEditor from '@/components/BasicSqlEditor.vue';
import { useOperationsStore } from '@/stores/operations';
import { MessagePlugin } from 'tdesign-vue';
import { to } from '@/utils/util';
import { cloneDeep } from 'lodash';

defineProps({
  datasourceOptions: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['fetchData']);

const operationsStore = useOperationsStore();

const isEditType = computed(() => mode.value === 'EDIT');

const checkName = async (val) => {
  if (isEditType.value && rawData.name === val) {
    return true;
  }
  if (!val) {
    return {
      result: false,
      message: '资源组名称不能为空',
      type: 'error',
    };
  }
  const { data } = await operationsStore.fetchSourceGroupCheckName(form.dataSourceId, val);
  if (data) {
    return {
      result: false,
      message: '资源组名称已存在',
      type: 'error',
    };
  }
  return true;
};
const initFormData = {
  dataSourceId: '',
  name: '',
  sourceSql: '',
};
const initFormRules = {
  dataSourceId: [
    {
      required: true,
      type: 'error',
      trigger: 'change',
    },
  ],
  name: [
    {
      required: true,
      type: 'error',
      trigger: 'change',
    },
    {
      whitespace: true,
      message: '不能为空',
    },
    {
      pattern: /^[^-]*$/,
      message: '不能输入中划线"-"',
    },
    { validator: checkName, trigger: 'change' },
  ],
};

// 表单数据
const { form, rules, formRef, validateForm, resetForm } = useForm(
  initFormData,
  initFormRules,
);

// 提交按钮禁用状态
const submitDisabled = computed(() => !(form.dataSourceId && form.name && form.sourceSql));

// 弹窗标题
const header = computed(() => (mode.value === 'ADD' ? '新增资源组' : '编辑资源组'));

let rawData;
const editCb = async (row) => {
  if (!(row.name && row.dataSourceId)) {
    return;
  }
  const [err, data] = await to(operationsStore.fetchSourceGroupGet(row.dataSourceId, row.name));
  if (err) {
    return;
  }
  Object.assign(form, { ...data, dataSourceId: row.dataSourceId });
  rawData = cloneDeep(data);
};

const closeCb = () => {
  resetForm();
};

const confirmCb = async () => {
  const valid = await validateForm();
  if (!valid) return;
  const regex = /CREATE\s+RESOURCE\s+(?:GROUP|group)\s+([^\s(]+)\s+(?:TO|to)/i;
  const match = form.sourceSql.match(regex);
  if (match && match[1] !== form.name) {
    MessagePlugin('error', '资源组名称与SQL中的名称不一致');
    return;
  }
  const params = { ...form };
  const api = mode.value === 'ADD' ? operationsStore.fetchSourceGroupAdd : operationsStore.fetchSourceGroupEdit;
  const [err] = await to(api(params));
  if (err) {
    return;
  }
  emit('fetchData');
  MessagePlugin('success', '操作成功');
  return true;
};

// 弹窗数据
const { mode, visible, openAddDialog, openEditDialog, onConfirm, onClose } = useDialog({
  editCb,
  confirmCb,
  closeCb,
});

const handleChangeDatasource = () => {
  validateForm(['name']);
};

defineExpose({ openAddDialog, openEditDialog });
</script>

<style lang="less" scoped>
.resource-wrap {
  height: 436px;
  margin-top: 24px;
}
</style>
