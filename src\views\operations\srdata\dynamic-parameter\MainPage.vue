<template>
  <div style="height: 100%">
    <PageLayout>
      <template #search>
        <BasicSearch v-model="searchKey" placeholder="参数名/参数值..." @reset="handleReset" @change="searchChange">
          <template #prefix>
            <t-select
              v-model="datasourceId"
              :options="datasourceOptions"
              placeholder="数据源"
              filterable
              @change="searchChange"
            ></t-select>
          </template>
        </BasicSearch>
      </template>

      <template #table>
        <t-table rowKey="id" :data="tableData" :columns="columns" :pagination="pagination" max-height="68vh">
          <template #empty v-if="!datasourceId">
            <no-data text="请先选择数据源"></no-data>
          </template>
        </t-table>
      </template>
    </PageLayout>

    <EditParamsDialog ref="dialogRef" :datasourceOptions="datasourceOptions" @fetchData="fetchData"></EditParamsDialog>
  </div>
</template>

<script setup lang="jsx" name="dynamic-parameter">
import { omit } from 'lodash';
import { ref, defineProps } from 'vue';
import { to } from '@/utils/util';
import { useSearch, usePagination } from '@/hooks';
import { useOperationsStore } from '@/stores/operations';
import { PageLayout } from '@/views/operations/components';
import BasicSearch from '@/components/BasicSearch.vue';
import EditParamsDialog from './EditParamsDialog.vue';
import NoData from '@/components/NoData.vue';

defineProps({
  datasourceOptions: {
    type: Array,
    default: () => [],
  },
});

const operationsStore = useOperationsStore();

const dialogRef = ref(null);

const datasourceId = ref('');

// 表格列
const columns = ref([
  { colKey: 'serial-number', width: 61, title: '序号', fixed: 'left' },
  {
    colKey: 'variableName',
    title: '参数名',
    ellipsis: true,
  },
  {
    colKey: 'value',
    title: '参数值',
    ellipsis: true,
  },
  {
    colKey: 'datasourceName',
    title: '所属SR数据源',
    ellipsis: true,
  },
  {
    title: '操作',
    colKey: 'operate',
    cell: (h, { row }) => (
      <t-space>
        <t-link theme="primary" hover="color" onClick={() => handleEdit(row) } >编辑 </t-link>
      </t-space>
    ),
  },
]);

const tableData = ref([]);

const fetchData = async () => {
  if (!datasourceId.value) return;
  const { current: pageNum, pageSize } = pagination;
  const params = {
    pageNum,
    pageSize,
    queryData: {
      queryKey: searchKey.value,
      datasourceId: datasourceId.value,
    },
  };
  const [err, data] = await to(operationsStore.fetchDynamicParamList(datasourceId.value, params));
  if (err) {
    return;
  }
  if (!data) {
    tableData.value = [];
    return;
  }
  const { total, list } = data;
  setPaginationTotal(total);
  tableData.value = list?.map(item => ({ ...omit(item, ['datasource']), datasourceId: item.datasource })) || [];
};

const { searchKey, setSearchKey, onSearch } = useSearch(fetchData);
const { pagination, setPaginationCurrent, setPaginationTotal } = usePagination(fetchData);

const searchChange = () => {
  setPaginationCurrent(1);
  onSearch();
};

const handleReset = () => {
  setSearchKey('');
  setPaginationCurrent(1);
  fetchData();
};

const handleEdit = (row) => {
  dialogRef.value.openEditDialog(row);
};
</script>

<style lang="less" scoped></style>
