<!-- 创建/编辑业务数据源 -->
<template>
  <div class="page-des-data-source-panel">
    <div class="form-wrap">
      <t-form ref="formRef" :data="form" :rules="rules" label-width="120px" style="width: 480px">
        <section style="margin-bottom: 16px;">
          <BlockHeader title="基本信息" style="margin-bottom: 12px"></BlockHeader>
          <t-form-item label="所属空间" name="spaceId">
            {{ space }}
          </t-form-item>
          <t-form-item label="创建人" name="creator">
            <UserName :full-name="form.createByName || staffDisplayName"></UserName>
          </t-form-item>
          <t-form-item label="负责人" name="inchargeList">
            <sdc-staff-selector ref="staffSelector" v-model="form.inchargeList" :disabled="disabled" @change="handleChangeInchargeList" multiple style="width:100%;" placeholder="请选择" modalClass="des-sdc-modal--fix" size="small"></sdc-staff-selector>
          </t-form-item>
          <t-form-item label="数据源类型" name="type">
            <t-select v-model="form.type" :options="options" filterable placeholder="请选择业务数据源类型" @change="() => changeFlag()" />
          </t-form-item>
          <t-form-item label="数据源名称" name="name">
            <t-input v-model="form.name" placeholder="请输入业务数据源名称" @change="() => changeFlag()" />
          </t-form-item>
          <t-form-item label="业务数据源标识" name="businessDatasourceFlag">
            <t-input v-model="form.businessDatasourceFlag" placeholder="请输入业务数据源标识" :disabled="isEditType" />
          </t-form-item>
          <t-form-item label="描述" name="description">
            <t-textarea
              v-model="form.description"
              placeholder="请输入描述内容"
              :maxlength="200"
              :autosize="{ minRows: 3, maxRows: 3 }"
            />
          </t-form-item>
          <t-form-item label="IP主机名" name="ip">
            <t-input v-model="form.ip" placeholder="请输入" @change="() => changeFlag()" />
          </t-form-item>
          <t-form-item label="端口" name="port">
            <t-input v-model="form.port" placeholder="请输入" @change="() => changeFlag()" />
          </t-form-item>
        </section>

        <section style="margin-bottom: 16px;">
          <BlockHeader title="安全认证" style="margin-bottom: 12px"></BlockHeader>
          <t-form-item label="用户名" name="userName">
            <t-input v-model="form.userName" placeholder="请输入" @change="() => changeFlag()" />
          </t-form-item>
          <t-form-item label="密码" name="passWord">
            <PasswrodInput v-model="form.passWord" :mode="mode" :inputing="inputing" @updateInputing="setInputing(true)" @change="() => changeFlag()"></PasswrodInput>
          </t-form-item>
        </section>

        <section>
          <BlockHeader title="高级设置" style="margin-bottom: 12px"></BlockHeader>
          <t-form-item label="数据库名" name="databaseName">
            <t-input v-model="form.databaseName" placeholder="请输入" @change="() => changeFlag()" />
          </t-form-item>
          <t-form-item label="jdbc连接参数" name="connectionParams">
            <t-input v-model="form.connectionParams" placeholder="key1=value1&key2=value2&..." />
          </t-form-item>
        </section>
      </t-form>
    </div>
    <BasicPageFooter height="72" @confirm="handleConfirm" @cancel="handleCancel">
      <template #behind>
        <t-button variant="outline" @click="handleTest">测试连接</t-button>
      </template>
    </BasicPageFooter>
  </div>
</template>

<script setup>
import { useForm, usePassword } from '@/hooks';
import { to } from '@/utils/util';
import eventBus, { EVENT_NAMES } from '@/utils/emitter';
import { pick, omit, cloneDeep } from 'lodash';
import { storeToRefs } from 'pinia';
import { MessagePlugin } from 'tdesign-vue';
import { useCommonStore } from '@/stores/common';
import { useSpacesStore } from '@/stores/spaces';
import { useDataCatalogStore } from '@/stores/data-catalog';
import { ref, defineProps, onMounted, computed, defineEmits } from 'vue';
import UserName from '@/components/UserName.vue';
import BlockHeader from '@/components/BlockHeader.vue';
import PasswrodInput from '@/components/PasswrodInput.vue';
import BasicPageFooter from '@/components/BasicPageFooter.vue';

const { inputing, setInputing } = usePassword();

const dataCatalogStore = useDataCatalogStore();
const { fetchBusiDatasourceTest, fetchBusiDatasourceAdd, fetchBusiDatasourceUpdate, fetchBusiDatasourceGet } = dataCatalogStore;
const commonStore = useCommonStore();
const spacesStore = useSpacesStore();
const { staffName, staffId, staffDisplayName } = storeToRefs(commonStore);
const { currentSpace, mySpaces, isSpaceOwner } = storeToRefs(spacesStore);

const emit = defineEmits(['remove', 'switch']);
const props = defineProps({
  id: String,
  tabValue: String,
});

// 编辑状态
const isEditType = computed(() => Boolean(props.id));
const mode = computed(() => (props.id ? 'EDIT' : 'ADD'));

// const showCreaterName = computed(() => (isEditType.value ? form.createByName : staffDisplayName.value));

// 当前用户的权限
const currentUserAuth = computed(() => {
  const target = form.grantList?.find(item => item.staffId === staffId.value);
  return target?.powerList.map(item => item.operateType) || [];
});

// 编辑时空间负责人可编辑、含有[授予]和[编辑]才可以编辑
const disabled = computed(() => {
  if (isEditType) {
    if (isSpaceOwner.value) {
      return false;
    }
    return !(currentUserAuth.value.includes('grant') && currentUserAuth.value.includes('edit'));
  }
  return false;
});

const space = computed(() => mySpaces.value.find(item => item.id === currentSpace.value)?.name);

const initFormData = {
  inchargeList: [],
  type: '',
  name: '',
  businessDatasourceFlag: '',
  description: '',
  ip: '',
  port: '',
  userName: '',
  passWord: '',
  databaseName: '',
  connectionParams: '',
};

const checkPassWord = (val) => {
  if ((mode.value === 'ADD' || inputing.value) && !val) {
    return {
      result: false,
      message: '请输入',
      type: 'error',
    };
  }
  return true;
};

const getRule = (array = []) => [
  {
    required: true,
    message: '必填',
    type: 'error',
  },
  {
    whitespace: true,
    message: '不能为空',
  },
  ...array,
];
const initFormRules = {
  inchargeList: [
    {
      required: true,
      message: '必选',
    },
  ],
  type: [
    {
      required: true,
      message: '必选',
    },
  ],
  name: getRule(),
  businessDatasourceFlag: getRule(),
  ip: getRule(),
  port: getRule(),
  userName: getRule(),
  passWord: [
    { whitespace: true, message: '不能为空' },
    { validator: checkPassWord, trigger: 'change' },
  ],
  databaseName: getRule(),
};
const { form, formRef, rules, validateForm } = useForm(initFormData, initFormRules);

const options = ref([
  { label: 'starrocks', value: 'starrocks' },
  { label: 'mysql', value: 'mysql' },
  { label: 'postgresql', value: 'postgresql' },
  { label: 'oracle', value: 'oracle' },
  // { label: 'http', value: 'http' },
]);

const selectedInchargeList = ref([]);
const handleChangeInchargeList = (list) => {
  selectedInchargeList.value = list;
};

const getInchargeItems = () => selectedInchargeList.value.map((item) => {
  const target = rowInchargeList.find(v => v.staffId === item.StaffID);
  // 后台有返回数据就原样返回
  if (target) {
    return target;
  }
  return {
    staffId: item.StaffID,
    staffName: item.EngName,
    staffDisplayName: item.StaffName,
    powerList: [{ operateType: 'grant' }],
  };
});

const getPostData = () => {
  const data = {
    ...form,
    inchargeList: getInchargeItems(),
    spaceId: currentSpace.value,
  };
  if (!isEditType) {
    data.createByName = staffDisplayName.value;
    data.createByStaffId = staffId.value;
  }
  if (data.connectionParams) {
    return data;
  }
  return omit(data, ['connectionParams']);
};

const handleConfirm = async () => {
  const valid = await validateForm();
  if (!valid) return;

  if (!flag) {
    MessagePlugin('error', '请测试连通性');
    return;
  }
  const params = { ...getPostData() };
  const api = isEditType.value ? fetchBusiDatasourceUpdate : fetchBusiDatasourceAdd;
  const [err] = await to(api(params));
  if (err) {
    return;
  }
  MessagePlugin('success', '操作成功');
  handleCancel();
  handleSwitch();
  handleRefresh();
};

// 关闭当前tab
const handleCancel = () => {
  emit('remove', { value: props.tabValue });
};
// 切换到指定的tab
const handleSwitch = () => {
  emit('switch', 'DataCatalog', {});
};
// 刷新业务管理源列表数据
const handleRefresh = () => {
  eventBus.emit(EVENT_NAMES.DATA_CATALOG.REFRESH_BUSINESS_DATASOURCE);
};

// 测试连通标识
let flag = false;
const changeFlag = (val = false) => {
  flag = val;
};

const handleTest = async () => {
  const fields = ['type', 'name', 'ip', 'port', 'userName', 'passWord', 'databaseName'];
  const valid = await validateForm({ fields });
  if (!valid) return;

  const params = { ...pick(form, [...fields, 'id']) };
  const [err] = await to(fetchBusiDatasourceTest(params));
  if (err) {
    return;
  }
  changeFlag(true);
  MessagePlugin('success', '测试连接成功');
};

const fetchData = async () => {
  if (!isEditType.value) return;
  const [err, data] = await to(fetchBusiDatasourceGet({ datasourceId: props.id }));
  if (err) {
    return;
  }
  Object.assign(form, data);
  initStaffSelector();
};

// 保存旧数据
let rowInchargeList = [];

const staffSelector = ref(null);
const initStaffSelector = () => {
  rowInchargeList = cloneDeep(form.inchargeList);

  selectedInchargeList.value = form.inchargeList.map(item => ({
    StaffName: item.staffDisplayName,
    StaffID: item.staffId,
    EngName: item.staffName,
  }));
  staffSelector.value.setSelected(selectedInchargeList.value);
};

// 新增时默认添加自己作为负责人
const addDefaultIncharge = () => {
  if (isEditType.value) {
    return;
  }
  Object.assign(form, {
    inchargeList: [
      {
        staffId: staffId.value,
        staffName: staffName.value,
        staffDisplayName: staffDisplayName.value,
        powerList: [{ operateType: 'grant' }],
      },
    ],
  });

  initStaffSelector();
};

onMounted(() => {
  fetchData();

  addDefaultIncharge();
});
</script>

<style lang="less" scoped>
.page-des-data-source-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  :deep(.form-wrap) {
    padding: 20px;
    height: calc(100% - 72px);
    overflow-y: auto;
    .t-form-item__creator, .t-form-item__spaceId {
      .t-form__controls-content {
        padding: 0 8px;
        background: #f2f2f2;
        border: 1px solid #ddd;
        border-radius: 4px;
      }
    }
  }
  :deep(.basic-page-footer__wrap) {
    justify-content: start;
    padding-left: 140px;
  }
}
</style>
