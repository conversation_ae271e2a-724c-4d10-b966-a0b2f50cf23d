<script setup>
import { cloneDeep, isEqual, pick } from 'lodash';
import { ref, reactive, onMounted, onBeforeUnmount, computed, defineProps, nextTick, defineEmits, provide, watchEffect, unref } from 'vue';
import { useRoute } from 'vue-router/composables';
import { storeToRefs } from 'pinia';
import { useCommonStore } from '@/stores/common';
import { Graph, Shape } from '@antv/x6';
import { SearchIcon, AddIcon, CheckIcon } from 'tdesign-icons-vue';
import { Selection } from '@antv/x6-plugin-selection';
import { Keyboard } from '@antv/x6-plugin-keyboard';
import { registerCustomNode, createNode, createEdge, createStreamNode, reLayout } from '@/utils/graph';
import { highlightMatchedText, findDuplicates } from '@/utils/util';
import emitter, { EVENT_NAMES } from '@/utils/emitter';
import { viewFlow, editFlow } from './utils/flow';
import { getScheduleLog, getExecuteLog, findDependency<PERSON>hain } from '../../utils/task';
import JobNode from './components/graph/JobNode.vue';
import TaskListPopup from './components/TaskListPopup.vue';
import { ViewLog } from '@/views/workbench/components';
import { showDialogConfirm } from '@/utils/dialog';
// 节点弹窗明细
import SubProcessDetail from './components/task-detail/SubProcess.vue';
import StarrocksMaterializedViewDetail from './components/task-detail/StarrocksMaterializedView.vue';
import StarrocksSqlDetail from './components/task-detail/StarrocksSql.vue';
import BIDSPlusTaskDetail from './components/task-detail/BIDSPlusTask.vue';
import JdbcSqlDetail from './components/task-detail/JdbcSql.vue';
import HttpDetail from './components/task-detail/HttpNode.vue';
import MessageDetail from './components/task-detail/MessageNode.vue';
// 保存工作流
import SaveWorkFlow from './components/SaveWorkFlow.vue';
import WorkFlowInfo from './components/WorkFlowInfo.vue';
import FlowVersion from '../../components/flow-define/FlowVersion.vue';
import DebugRecord from './components/DebugRecord.vue';
import { showDebug } from './utils/task';
import { SUBPROCESS, STARROCKS_MATERIALIZED_VIEW, STARROCKS_SQL, BIDSPLUS, JDBC_SQL, HTTP, MESSAGE, taskTypeLabelMapping, taskTypeColorMapping, taskTypeCodeMapping } from './utils/task-type';
import { validateList } from './utils/rules';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue';
import { useDataDevelopmentStore } from '@/stores/data-development';

const props = defineProps({
  panel: {
    type: String,
  },
  // 编辑状态下才有
  id: {
    type: String,
  },
  code: {
    type: String,
  },
  name: {
    type: String,
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  // 默认选中的节点ID
  defaultSelectNodeId: {
    type: String,
    default: '',
  },
  // 编辑态下才有传参
  isDraft: {
    type: Boolean,
    default: false,
  },
  // 查看历史版本才有传参
  isHistory: {
    type: Boolean,
    default: false,
  },
  // 工作流实例id, 从[工作流实例标识]点击进来才有
  processInstanceId: {
    type: String,
  },
  // 工作流实例状态, 从[工作流实例标识]点击进来才有
  processInstanceState: {
    type: String,
  },
});
const emit = defineEmits(['remove']);
const ready = ref(false);

const saveWorkFlowRef = ref(null);
const workFlowInfoRef = ref(null);
const flowVersionRef = ref(null);
const viewLogRef = ref(null);
const debugRecordRef = ref(null);

const commonStore = useCommonStore();
const { expand } = storeToRefs(commonStore);
const dataDevelopmentStore = useDataDevelopmentStore();
const route = useRoute();
// 工作流配置
const flowData = reactive({
  id: '',
  name: '',
  code: '',
  description: '',
  timeout: '',
  alertChannels: [],
  alertConfigId: [],
  executionType: '',
  globalParams: [],
});

const lineType = ref('horizontal'); // 'vertical'

const permissionList = ref([]);
function hasOperateRight(operate) {
  return permissionList.value.includes(operate);
}

const taskCompMapping = {
  [SUBPROCESS]: SubProcessDetail,
  [STARROCKS_MATERIALIZED_VIEW]: StarrocksMaterializedViewDetail,
  [STARROCKS_SQL]: StarrocksSqlDetail,
  [BIDSPLUS]: BIDSPlusTaskDetail,
  [JDBC_SQL]: JdbcSqlDetail,
  [HTTP]: HttpDetail,
  [MESSAGE]: MessageDetail,
};
const paintboardRef = ref(null);
const searchKey = ref('');

const graph = ref(null);
const currentNode = ref(null);
const currentNodeName = ref('');
// 当前节点数据
const currentNodeData = ref({});
// 深克隆当前节点数据 用于比较数据
let currentRawNodeData = {};
// 节点明细查看
const visibleDetail = ref(false);
// 任务定义数据
const taskDefinitionsDataMap = ref({});
const nodeList = ref([]);
// 实现自定义搜索option的label，不区分大小写
const searchNodeList = computed(() => nodeList.value.filter(item => item.label.toLowerCase().includes(searchKey.value.toLowerCase())));

// 用于数据比较
const rowData = ref({});

provide('graph', graph);
provide('nodeList', nodeList);

// 自动重新定位节点
watchEffect(() => {
  if (ready.value && props.defaultSelectNodeId) {
    selectNode(props.defaultSelectNodeId);
  }
});

registerCustomNode('job-node', JobNode, {
  direction: lineType.value,
});

function initPaintboard() {
  graph.value = new Graph({
    container: paintboardRef.value,
    autoResize: true,
    // 允许拖拽
    panning: {
      enabled: true,
    },
    // 允许缩放
    mousewheel: {
      enabled: true, // 启用滚轮缩放
      zoomAtMousePosition: true, // 缩放以鼠标位置为中心
      minScale: 0.5, // 最小缩放比例
      maxScale: 2, // 最大缩放比例
    },
    // 开启网格
    grid: {
      visible: true,
    },
    background: {
      color: '#F5F7F9',
    },
    // 连线
    connecting: {
      snap: {
        radius: 20,
      },
      // 不允许空连接
      allowBlank: false,
      // 不允许自己连自己
      allowLoop: false,
      router: 'manhattan',
      connector: {
        name: 'rounded',
        args: {
          radius: 8,
        },
      },
      anchor: 'center',
      connectionPoint: 'anchor',
      createEdge() {
        return new Shape.Edge({
          attrs: {
            line: {
              stroke: '#3464E0',
              strokeWidth: 1,
              targetMarker: {
                name: 'block',
                width: 12,
                height: 8,
              },
            },
          },
          zIndex: -1,
        });
      },
      // 连接桩校验
      validateConnection({ sourceMagnet, targetMagnet }) {
        // 只能从输出链接桩创建连接
        if (!sourceMagnet || sourceMagnet.getAttribute('port-group') === 'in') {
          return false;
        }
        // 只能连接到输入链接桩
        if (!targetMagnet || targetMagnet.getAttribute('port-group') !== 'in') {
          return false;
        }
        return true;
      },
    },
  });

  // 开启选中功能
  graph.value
    .use(new Selection({
      multiple: false,
    }))
    .use(new Keyboard());
  // 删除
  graph.value.bindKey('backspace', () => {
    if (props.readonly) return;
    const cells = graph.value.getSelectedCells();
    if (cells.length) {
      graph.value.removeCells(cells);
      cells.forEach((cell) => {
        const index = nodeList.value.findIndex(n => n.value === cell.id);
        if (index !== -1) {
          nodeList.value.splice(index, 1);
        }
      });
    }
  });
}

function refreshPanelData() {
  // 清空画布的节点和边
  graph.value.clearCells();
  // 清空旧节点数据
  nodeList.value = [];
  initModifyPanelData();
}

async function initModifyPanelData() {
  if (props.processInstanceId) {
    const params = { spaceId: route.query.spaceId, code: props.code, processInstanceId: props.processInstanceId };
    const { data } = await dataDevelopmentStore.fetchFlowInstanceDetail(params);
    initPanelData(data);
    return;
  }
  if (props.isHistory) {
    const { data } = await dataDevelopmentStore.fetchFlowDefineVersionGet(props.id);
    initPanelData(data);
    return;
  }
  if (props.code && route.query.spaceId) {
    const { data } = await dataDevelopmentStore.fetchFlowDefineDetail(route.query.spaceId, props.code);
    initPanelData(data);
  }
}
// 定义所有事件处理函数
function handleGraphNodeLog(event) {
  if (event.panel === props.panel) {
    const { taskInstanceCode, processCode, startTime } = event.data.data.taskInfo;
    const row = { taskInstanceCode, processCode, startTime };
    viewLogRef.value.open(row);
  }
}

function handleGraphNodeEdit(event) {
  if (event.panel === props.panel) {
    editNode(event.data);
  }
}

function handleGraphNodeDelete(event) {
  if (event.panel === props.panel) {
    removeNode(event.data);
  }
}

function handleGraphNodeView(event) {
  if (event.panel === props.panel) {
    editNode(event.data);
  }
}

function handleGraphNodeAdd(event) {
  if (event.panel === props.panel) {
    addStreamNode(event.data.node, event.data.direction, event.data.parent, event.data.item);
  }
}

function handleGraphDataCompare(event) {
  if (event.panel === props.panel) {
    const equal = compareData();
    dataDevelopmentStore.setFlowDataIsEqual(equal);
  }
}

function handleGraphNodeSelect(event) {
  if (event.panel === props.panel) {
    selectNode(event.defaultSelectNodeId);
  }
}

async function handleGraphNodeDebug(event) {
  if (event.panel === props.panel) {
    const result = await fetchDebug(event.data.data);
    if (result) {
      debugRecordRef.value.open(result);
    }
  }
}

async function fetchDebug(data) {
  const subData = getDebugSubData(data.code);
  if (!subData) {
    MessagePlugin.error('当前节点信息填写不完整');
    return;
  }
  try {
    const result = await dataDevelopmentStore.fetchFlowDebug({ spaceId: route.query.spaceId, processCode: flowData.code, taskCode: data.code }, subData);
    MessagePlugin.success('操作成功');
    return result.data;
  } catch (error) {}
}

onMounted(async () => {
  // 注册所有事件监听 编辑、删除、查看、添加、数据比较
  emitter.on(EVENT_NAMES.WORK_FLOW_PANEL.VIEW_TASK_LOG, handleGraphNodeLog);
  emitter.on(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_NODE_EDIT, handleGraphNodeEdit);
  emitter.on(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_NODE_DELETE, handleGraphNodeDelete);
  emitter.on(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_NODE_VIEW, handleGraphNodeView);
  emitter.on(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_NODE_ADD, handleGraphNodeAdd);
  emitter.on(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_DATA_COMPARE, handleGraphDataCompare);
  emitter.on(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_NODE_SELECT, handleGraphNodeSelect);
  emitter.on(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_NODE_DEBUG, handleGraphNodeDebug);

  await initPaintboard();
  await initModifyPanelData();

  ready.value = true;
});

onBeforeUnmount(() => {
  handleUnlock();
  // 注销所有事件监听
  emitter.off(EVENT_NAMES.WORK_FLOW_PANEL.VIEW_TASK_LOG, handleGraphNodeLog);
  emitter.off(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_NODE_EDIT, handleGraphNodeEdit);
  emitter.off(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_NODE_DELETE, handleGraphNodeDelete);
  emitter.off(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_NODE_VIEW, handleGraphNodeView);
  emitter.off(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_NODE_ADD, handleGraphNodeAdd);
  emitter.off(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_DATA_COMPARE, handleGraphDataCompare);
  emitter.off(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_NODE_SELECT, handleGraphNodeSelect);
  emitter.off(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_NODE_DEBUG, handleGraphNodeDebug);
});
// 生成节点名称
function generateNodeName(name) {
  // 如果节点中存在该名称，则自增
  const matched = nodeList.value.find(n => n.label === name);
  if (matched) {
    const count = matched.label.match(/_(\d+)$/)?.[1] || 0;
    if (count > 0) {
      return generateNodeName(`${name.replace(/(\d+)$/, '')}${parseInt(count, 10) + 1}`);
    }
    return generateNodeName(`${name}_${parseInt(count, 10) + 1}`);
  }
  return name;
}
// 生成节点标识
function generateNodeCode(type) {
  const array = nodeList.value.map(n => n.code);
  // 提取每个元素中"-"后面的数字部分
  const numbers = array.map((item) => {
    // 匹配"-"后面的所有数字
    const match = item.match(/-(\d+)/);
    // 如果匹配成功则转换为数字，否则返回0
    return match ? parseInt(match[1], 10) : 0;
  });
  // 找出最大的数字
  const maxNumber = Math.max(0, ...numbers);
  const code = `${taskTypeCodeMapping[type]}-${maxNumber + 1}`;
  return code;
}
// 查找第一个节点的位置
function findNodePostion() {
  const { locations, taskRelations } = getNodeLocationsAndRelations();
  const taskDependencyChain = findDependencyChain(taskRelations);
  if (taskDependencyChain.length) {
    const [firstTaskCode] = taskDependencyChain;
    return locations.find(item => item.taskCode === firstTaskCode) || { x: 200, y: 200 };
  }
  return { x: 200, y: 200 };
}
function addFreeNode(parent, item) {
  const title = generateNodeName(item.label);
  const { x, y } = findNodePostion();
  // 随机偏移一些值，避免节点重叠
  const offsetX = Math.floor(Math.random() * 100);
  const offsetY = Math.floor(Math.random() * 100);
  const newNode = createNode('job-node', {
    title,
    type: item.value,
    color: parent.color,
    panel: props.panel,
    direction: lineType.value,
    readonly: props.readonly,
    code: generateNodeCode(item.value),
  }, {
    x: x + offsetX,
    y: y + offsetY,
  }, graph.value);
  nodeList.value.push({
    label: title,
    value: newNode.id,
    color: parent.color,
    code: generateNodeCode(item.value),
  });
  addTaskData(newNode);
}
function editNode(node) {
  const nodeData = node.getData();
  currentNode.value = nodeData.type;
  currentNodeName.value = nodeData.title;
  visibleDetail.value = true;
  const taskData = taskDefinitionsDataMap.value[node.id];
  console.log('currentNodeData', taskData);
  currentNodeData.value = cloneDeep(taskData);
  currentRawNodeData = cloneDeep(taskData);
  nextTick(() => {
    refMap[currentNode.value]?.setData(node.id, cloneDeep(taskData));
  });
}
// 子元素的添加节点
function addStreamNode(node, type, parent, item) {
  const title = generateNodeName(item.label);
  const newNode = createStreamNode(
    type === 'right' ? 'next' : 'prev',
    'job-node',
    {
      title,
      type: item.value,
      color: parent.color,
      panel: props.panel,
      direction: lineType.value,
      readonly: props.readonly,
      code: generateNodeCode(item.value),
    },
    node,
    graph.value,
    lineType.value,
  );
  nodeList.value.push({
    label: title,
    value: newNode.id,
    color: parent.color,
    code: generateNodeCode(item.value),
  });
  addTaskData(newNode);
}
// 删除节点
function removeNode(node) {
  nodeList.value = nodeList.value.filter(n => n.value !== node.id);
  removeTaskData(node);
}
// 添加节点对应的数据
function addTaskData(node) {
  const nodeData = node.getData();
  taskDefinitionsDataMap.value[node.id] = {
    _id: node.id,
    taskType: nodeData.type,
    name: nodeData.title,
    code: nodeData.code,
  };
}
// 删除节点对应的数据
function removeTaskData(node) {
  delete taskDefinitionsDataMap.value[node.id];
}
const refMap = {};
function setRefMap(el, current) {
  if (el) {
    refMap[current] = el;
  }
}
async function handleDebugNode() {
  const done = await handleSaveNode();
  if (done) {
    const result = await fetchDebug(currentNodeData.value);
    debugRecordRef.value.open(result);
  }
}
async function handleCancelNode() {
  visibleDetail.value = false;
}
async function handleSaveNode() {
  const data = await refMap[currentNode.value].save();
  if (data) {
    taskDefinitionsDataMap.value[data.nodeId] = {
      _id: data.nodeId,
      ...data.data,
    };
    // 给节点重新设置名称
    const node = graph.value.getCellById(data.nodeId);
    node.setData({
      title: data.data.name,
      code: data.data.code,
    });
    // 更新nodeList的数据(前置任务的选项)
    nodeList.value.forEach((node) => {
      if (node.value === data.nodeId) {
        // eslint-disable-next-line no-param-reassign
        node.label = data.data.name;
      }
    });
    // 画布前置任务连线
    emitter.emit(EVENT_NAMES.WORK_FLOW_PANEL.LINK_PRE_TASK, { currentFlowName: props.name });
    handleCancelNode();
    return true;
  }
  return false;
}
// 查找节点流程是否循环
function findLoop() {
  if (nodeList.value.length === 0) {
    return false;
  }
  // 使用Set存储有来源的任务节点标识（自动去重）
  const hasSourceTaskeCode = new Set();
  graph.value.getEdges().forEach((edge) => {
    const targetNode = edge.getTargetNode();
    const targetNodeData = targetNode.getData();
    // 因为这里是通过读取边数据，所以边数据有target就等于节点有来源
    hasSourceTaskeCode.add(targetNodeData.code);
  });
  if (hasSourceTaskeCode.size === nodeList.value.length) {
    return true;
  }
  return false;
}
function validateBeforeSave() {
  const taskList = nodeList.value.map(node => taskDefinitionsDataMap.value[node.value]);
  const validate = validateList(taskList);
  if (!validate.result) {
    selectNode(validate.nodeId);
    MessagePlugin('error', `节点：${validate.nodeName} 信息填写不完整`);
    return;
  }
  const loop = findLoop();
  if (loop) {
    MessagePlugin('error', '节点流程循环');
    return;
  }
  const duplicates = findDuplicates(taskList.map(item => item.name));
  if (duplicates.length) {
    MessagePlugin('error', `节点名称：${duplicates.join(',')} 重复`);
    return;
  }
  const codeDuplicates = findDuplicates(taskList.map(item => item.code));
  if (codeDuplicates.length) {
    MessagePlugin('error', `节点标识：${codeDuplicates.join(',')} 重复`);
    return;
  }
  return true;
}
function handleSave() {
  const valid = validateBeforeSave();
  if (!valid) return;
  saveWorkFlowRef.value.open(flowData);
}
async function handlePublish(response) {
  const { data } = response;
  if (!data.permissionList?.includes('RUN')) return;
  if (data.taskDefinitions.length === 0) return;
  const { type } = await showDialogConfirm({
    title: `工作流${data.name}已提交，是否直接发布`,
    width: '440px',
  });
  if (type === 'success') {
    try {
      const { data: key } = await dataDevelopmentStore.publishFlowDefine(route.query.spaceId, data.code);
      showAsyncPublishAlter(data, key);
    } catch (error) {}
  }
}
async function showAsyncPublishAlter(row, key) {
  let pubSuccess = await getPublishResult(key);
  const getHeader = () => (pubSuccess ? `工作流「${row.name}」发布成功` : `工作流「${row.name}」发布中`);
  const getBody = () => (pubSuccess ? '工作流发布成功' : '工作流发布中，请等待，若关闭需手动刷新列表查看发布结果');
  const header = getHeader();
  const body = getBody();
  const dialog = DialogPlugin.alert({
    theme: 'info',
    header,
    body,
    className: 'des-common-dialog-plugin',
    confirmBtn: {
      content: '我知道了',
    },
    cancelBtn: null,
    closeBtn: false,
    width: 440,
    onConfirm: () => {
      if (pubSuccess) {
        emitter.emit(EVENT_NAMES.DATA_DEVELOPMENT.REFRESH_FLOW_DEFINE);
      }
      clearInterval(timerId);
      dialog.destroy();
    },
  });
  const timerId = setInterval(async () => {
    if (pubSuccess) {
      clearInterval(timerId);
      return;
    }
    pubSuccess = await getPublishResult(key);
    dialog.update({
      header: getHeader(),
      body: getBody(),
    });
  }, 2000);
}
async function getPublishResult(key) {
  const { data } = await dataDevelopmentStore.fetchFlowDefineBatchPublishResult(route.query.spaceId, { key });
  return Boolean(data.length) && data.every(item => item.status !== 'PUBLISHING');
}
async function handleSubmitWorkFlow(data) {
  flowData.id = data.id;
  flowData.name = data.name;
  flowData.code = data.code;
  flowData.warehouseLayer = data.warehouseLayer;
  flowData.businessType = data.businessType;
  flowData.description = data.description;
  flowData.alertChannels = data.alertChannels;
  flowData.timeout = data.timeout;
  flowData.alertConfigId = data.alertConfigId;
  flowData.executionType = data.executionType;
  flowData.globalParams = data.globalParams;
  const subData = getSubData();
  console.log('subData:', subData);
  // 提交数据
  let response;
  if (props.code) {
    response = await dataDevelopmentStore.updateFlowDefine(route.query.spaceId, props.code, subData);
  } else {
    response = await dataDevelopmentStore.createFlowDefine(route.query.spaceId, subData);
  }
  // 关闭提教工作流的弹窗
  saveWorkFlowRef.value.close();
  // 快捷发布
  await handlePublish(response);
  emit('remove', { value: props.panel }, { showConfirm: false });
  // data-development中监听触发获取列表数据, 关闭此处避免重复调用接口
  // emitter.emit(EVENT_NAMES.DATA_DEVELOPMENT.REFRESH_FLOW_DEFINE);
}
function getSubData() {
  const taskList = nodeList.value.map(node => taskDefinitionsDataMap.value[node.value]);
  const data = {
    lineType: lineType.value.toUpperCase(),
    id: flowData.id,
    code: flowData.code,
    warehouseLayer: flowData.warehouseLayer,
    businessType: flowData.businessType,
    spaceId: route.query.spaceId,
    name: flowData.name,
    description: flowData.description,
    timeoutFlag: flowData.timeoutFlag,
    alertChannels: flowData.alertChannels,
    timeout: flowData.timeout,
    alertConfigId: flowData.alertConfigId,
    executionType: flowData.executionType,
    globalParams: flowData.globalParams,
    taskDefinitions: taskList.map((taskData) => {
      const { _id, taskProperties, ...task } = taskData;
      return {
        ...task,
        // 转换成数组
        taskProperties: Object.keys(taskProperties).map(prop => ({
          prop,
          value: taskProperties[prop],
        })),
      };
    }),
    ...getNodeLocationsAndRelations(),
  };
  return data;
}
function getDebugSubData(taskCode) {
  const taskList = nodeList.value.map(node => taskDefinitionsDataMap.value[node.value]).filter(item => item.code === taskCode);
  const [currentTask] = taskList;
  if (!currentTask.taskProperties) {
    return;
  }
  const data = {
    lineType: lineType.value.toUpperCase(),
    id: flowData.id,
    code: flowData.code,
    warehouseLayer: flowData.warehouseLayer,
    businessType: flowData.businessType,
    spaceId: route.query.spaceId,
    name: flowData.name,
    description: flowData.description,
    timeoutFlag: flowData.timeoutFlag,
    alertChannels: flowData.alertChannels,
    timeout: flowData.timeout,
    alertConfigId: flowData.alertConfigId,
    executionType: flowData.executionType,
    globalParams: flowData.globalParams,
    taskDefinitions: taskList.map((taskData) => {
      const { _id, taskProperties, ...task } = taskData;
      return {
        ...task,
        // 转换成数组
        taskProperties: Object.keys(taskProperties).map(prop => ({
          prop,
          value: taskProperties[prop],
        })),
      };
    }),
    ...getNodeLocationsAndRelations(),
  };
  return data;
}
function getNodeLocationsAndRelations() {
  const locations = [];
  const taskRelations = [];
  // 所有节点标识
  const taskCodes = [];
  graph.value.getNodes().forEach((node) => {
    const nodeData = node.getData();
    const nodePosition = node.getPosition();
    locations.push({
      taskCode: nodeData.code,
      x: nodePosition.x,
      y: nodePosition.y,
    });
    taskCodes.push(nodeData.code);
  });
  // 有来源的任务节点标识
  const hasSourceTaskeCode = [];
  graph.value.getEdges().forEach((edge) => {
    const sourceNode = edge.getSourceNode();
    const targetNode = edge.getTargetNode();
    const sourceNodeData = sourceNode.getData();
    const targetNodeData = targetNode.getData();
    // 因为这里是通过读取边数据，所以边数据有target就等于节点有来源
    hasSourceTaskeCode.push(targetNodeData.code);
    taskRelations.push({
      preTaskCode: sourceNodeData.code,
      postTaskCode: targetNodeData.code,
    });
  });
  // 对比taskCodes和hasSourceTaskeCode，找出没有来源的任务节点名称
  const noSourceTaskeName = taskCodes.filter(taskCode => !hasSourceTaskeCode.includes(taskCode));
  noSourceTaskeName.forEach((taskCode) => {
    taskRelations.push({ preTaskCode: '0', postTaskCode: taskCode });
  });
  return {
    locations,
    taskRelations,
  };
}
// 初始化报表数据
function initPanelData(data = {}) {
  // 看板基本信息
  flowData.id = data.id;
  flowData.name = data.name;
  flowData.code = data.code;
  flowData.warehouseLayer = data.warehouseLayer;
  flowData.businessType = data.businessType;
  flowData.description = data.description;
  flowData.timeoutFlag = data.timeoutFlag;
  flowData.alertChannels = data.alertChannels || [];
  flowData.timeout = data.timeout;
  flowData.alertConfigId = data.alertConfigId || [];
  flowData.executionType = data.executionType;
  flowData.globalParams = data.globalParams;
  lineType.value = data.lineType.toLowerCase();
  permissionList.value = data.permissionList;
  // 调整注册节点的桩位置(默认horizontal)
  if (lineType.value !== 'horizontal') {
    registerCustomNode('job-node', JobNode, {
      direction: lineType.value,
    });
  }
  parseNodeData(data);
}
// 还原节点数据nodeList和taskDefinitionsDataMap
// 解析位置信息和关系信息，还原图形
function parseNodeData(data = {}) {
  console.log('parseNodeData', data);
  const codeMapping = {};
  const locationMapping = {};
  const { taskDefinitions = [], locations = [], taskRelations = [] } = data;
  locations.forEach((location) => {
    locationMapping[location.taskCode] = {
      x: Number(location.x),
      y: Number(location.y),
    };
  });
  taskDefinitions.forEach((taskData) => {
    const { id, taskType, name, code, taskInstanceDTO = null } = taskData;
    const taskProperties = {};
    taskData.taskProperties.forEach((prop) => {
      taskProperties[prop.prop] = prop.value;
    });
    taskDefinitionsDataMap.value[id] = {
      _id: id,
      taskType,
      name,
      code,
      ...taskData,
      taskProperties,
    };
    nodeList.value.push({
      label: name,
      value: id,
      color: taskTypeColorMapping[taskType],
      code,
    });
    codeMapping[code] = id;
    createNode('job-node', {
      title: name,
      code,
      type: taskType,
      color: taskTypeColorMapping[taskType],
      panel: props.panel,
      direction: lineType.value,
      readonly: props.readonly,
      taskInfo: taskInstanceDTO, // 任务实例的信息, 从工作流实例标识点击进来才有
    }, locationMapping[code], graph.value, id);
  });
  // 绘制连线
  taskRelations.forEach((item) => {
    const { preTaskCode, postTaskCode } = item;
    // taskRelations为0的是起始节点，不需要连线
    if (preTaskCode !== '0') {
      const preTaskId = codeMapping[preTaskCode];
      const postTaskId = codeMapping[postTaskCode];
      createEdge(preTaskId, postTaskId, graph.value);
    }
  });
  // 回显储存数据做比较
  rowData.value = cloneDeep({
    taskDefinitionsDataMap: unref(taskDefinitionsDataMap),
    taskRelations: taskRelations.map(item => ({ postTaskCode: item.postTaskCode, preTaskCode: item.preTaskCode })),
  });
  adjustView();
}
function adjustView(padding = 50) {
  graph.value.zoomToFit({
    padding,
    maxScale: 1,
  });
}
function handleViewBaseInfo() {
  console.log('查看基本信息');
  workFlowInfoRef.value.open();
}
function handleViewVersionInfo() {
  console.log('查看版本信息');
  flowVersionRef.value.open({
    processCode: flowData.code,
    processId: flowData.id,
    onWorkFlowPanel: true,
  });
}
// function handleDownload() {
// }
function handleToggleZoom() {
  commonStore.toggleExpand();
}
function handleFormat() {
  reLayout(graph.value, {
    direction: lineType.value,
  });
}
function handleZoomToFit() {
  adjustView(100);
}
function handleSetLayout(direction) {
  // 调整注册节点的桩位置
  registerCustomNode('job-node', JobNode, {
    direction,
  });
  // 记录线条连接的目标，一会重连
  const edges = graph.value.getEdges().map(edge => ({
    source: edge.getSourceNode().id,
    target: edge.getTargetNode().id,
  }));
  // 修改存量节点桩的位置
  graph.value.getNodes().forEach((node) => {
    // 删除节点重新创建
    const nodeData = node.getData();
    const nodePosition = node.getPosition();
    node.remove();
    createNode('job-node', {
      title: nodeData.title,
      code: nodeData.code,
      type: nodeData.type,
      color: nodeData.color,
      panel: props.panel,
      direction: lineType.value,
      readonly: props.readonly,
      taskInfo: nodeData.taskInfo, // 任务实例的信息, 从工作流实例标识点击进来才有
    }, nodePosition, graph.value, node.id);
  });
  // 重新连线
  edges.forEach((edge) => {
    const { source, target } = edge;
    createEdge(source, target, graph.value);
  });
  // 重排节点位置
  handleFormat();
}
function updateSearchKey(keyword) {
  searchKey.value = keyword;
}
function selectNode(id = '') {
  if (id) {
    const node = graph.value.getCellById(id);
    const nodePosition = node.getPosition();
    graph.value.centerPoint(nodePosition.x + 120, nodePosition.y + 34);
    graph.value.select(node);
  }
}
function handleSearchNode(item) {
  selectNode(item.value);
}

const popupProps = reactive({
  overlayStyle: {
    width: '196px',  // 设置下拉面板宽度
    maxWidth: '196px', // 同时设置最大宽度确保生效
    maxHeight: '400px', // 限制最大高度
    'overflow-y': 'auto',  // 必须添加滚动条控制
    'overflow-x': 'hidden',  // 必须添加滚动条控制
  },
});

async function handleSaveDraft() {
  const valid = validateBeforeSave();
  if (!valid) return;
  const subData = getSubData();
  await dataDevelopmentStore.saveFlowDefineDraft(route.query.spaceId, props.code, subData);
  // 更新数据做比较
  rowData.value = cloneDeep({ ...pick(subData, ['taskRelations']), taskDefinitionsDataMap: unref(taskDefinitionsDataMap) });
  MessagePlugin.success('保存草稿成功');
}

function compareData() {
  const { taskRelations } = getNodeLocationsAndRelations();
  const data = { taskRelations, taskDefinitionsDataMap: unref(taskDefinitionsDataMap) };
  const equal = isEqual(rowData.value, data);
  return equal;
}

const showGoSubFlow = computed(() => [SUBPROCESS].includes(currentNode.value));
const disabledSubFlow = computed(() => {
  // 如果是只读模式，直接返回false
  if (props.readonly) return false;
  // 如果没有任务属性，返回true
  if (!currentRawNodeData.taskProperties) return true;
  // 比较流程定义名称是否相同
  return currentRawNodeData.taskProperties.processDefinitionName !==  currentNodeData.value.taskProperties.processDefinitionName;
});
async function handleGoSubFlow() {
  // 判断是否工作流实例 进入子节点实例
  if (props.processInstanceId) {
    const params = {
      spaceId: route.query.spaceId,
      taskInstanceCode: currentNodeData.value.taskInstanceDTO.taskInstanceCode,
    };
    const { data } = await dataDevelopmentStore.fetchFlowInstanceSubInstanceInfo(params);
    if (data) {
      const subFlowData = {
        id: data.processId,
        name: data.jobNameNew,
        code: data.jobName,
        processInstanceId: data.jobInstanceId,
        processInstanceState: data.jobState,
      };
      dataDevelopmentStore.goViewFlowInstance(subFlowData);
      handleCancelNode();
      return;
    }
    MessagePlugin.error('子节点实例不存在');
    return;
  }
  const subFlowData = await refMap[currentNode.value]?.getSubFlowData();
  // 统一权限检查
  const checkPermission = (permission) => {
    const { permissionList } = subFlowData;
    if (!permissionList.includes(permission)) {
      MessagePlugin.warning('无权限');
      return false;
    }
    return true;
  };
  // 只读模式处理
  if (props.readonly) {
    if (checkPermission('READ')) {
      viewFlow(subFlowData);
      handleCancelNode();
    }
    return;
  }

  // 编辑模式处理
  if (checkPermission('EDIT')) {
    editFlow(subFlowData, route.query.spaceId);
    handleCancelNode();
  }
}
function updateProcessDefinitionName(val) {
  currentNodeData.value = {
    ...currentNodeData.value,
    taskProperties: {
      ...currentNodeData.value.taskProperties,
      processDefinitionName: val,
    },
  };
}
// 工作流解锁
async function handleUnlock() {
  // 查看模式禁止解锁
  if (props.readonly) return;
  // 新增模式禁止解锁
  if (!props.id) return;
  await dataDevelopmentStore.fetchFlowDefineUnlock(route.query.spaceId, flowData.code);
}

async function handleRerun(opType) {
  const text = opType === 'restart_fails' ? '失败任务' : '';
  const result = await showDialogConfirm({
    title: `确定重跑工作流实例${text}吗？`,
    width: '440px',
  });
  if (result.type === 'cancel') return;
  operateFunc(opType);
}

async function operateFunc(opType) {
  const params = {
    spaceId: route.query.spaceId,
    jobInstanceIds: [props.processInstanceId],
    opType,
    jobCode: props.code,
  };
  const { data } = await dataDevelopmentStore.fetchFlowInstanceOperate(params);
  if (data) {
    MessagePlugin('success', '操作成功');
  };
}

function handleEditFlow() {
  const data = {
    id: flowData.id,
    name: flowData.name,
    code: flowData.code,
  };
  editFlow(data, route.query.spaceId);
}

function handleOpenDebugRecord() {
  debugRecordRef.value.open();
}
</script>

<template>
  <div class="page-des-work-flow-panel">
    <div class="header">
      <div class="header-left">
        <t-space size="8px">
          <t-button v-if="!readonly" @click="handleSave"><CheckIcon slot="icon" />提交</t-button>
          <!-- 编辑才有保存草稿 -->
          <t-button v-if="id && !readonly" variant="outline" @click="handleSaveDraft">保存草稿</t-button>
          <t-button v-if="id" variant="outline" @click="handleViewBaseInfo">基本信息</t-button>
          <!-- 查看<工作流实例>才有 -->
          <t-button v-if="id && processInstanceId" variant="outline" @click="handleRerun('restart')" :disabled="processInstanceState === 'running' || !hasOperateRight('RUN')">重跑</t-button>
          <t-button v-if="id && processInstanceId" variant="outline" @click="handleRerun('restart_fails')" :disabled="processInstanceState !== 'failed' || !hasOperateRight('RUN')">重跑失败任务</t-button>
          <t-button v-if="id && processInstanceId" variant="outline" @click="handleEditFlow" :disabled="!hasOperateRight('EDIT')">编辑工作流</t-button>
          <t-button v-if="id && processInstanceId" variant="outline" @click="refreshPanelData">刷新</t-button>
        </t-space>
        <div class="draft-wrap" v-if="isDraft">
          <span class="draft-text">草稿中</span>
        </div>
      </div>
      <t-link v-if="id && !isHistory" theme="primary" hover="color" size="small" @click="handleViewVersionInfo"><des-icon name="des-icon-xinxishizhi" slot="prefix-icon"></des-icon>版本信息</t-link>
    </div>
    <div class="paintboard">
      <div ref="paintboardRef" class="paintboard-inner"></div>
      <task-list-popup @clickNode="addFreeNode" v-if="!readonly">
        <t-button class="add-task-btn" variant="outline">
          <add-icon slot="icon" />任务节点
        </t-button>
      </task-list-popup>
      <div class="paintboard-tools">
        <t-select-input v-model="searchKey" placeholder="节点搜索..." allow-input :popupProps="popupProps" @input-change="updateSearchKey">
          <template #suffixIcon>
            <search-icon :style="{ cursor: 'pointer' }" />
          </template>
          <template #panel>
            <ul class="search-items" v-if="searchNodeList.length">
              <li v-for="item in searchNodeList" :key="item.value" @click="handleSearchNode(item)">
                <des-icon name="des-icon-linwujiedian" size="16" :style="{ marginRight: '8px', color: item.color }"></des-icon>
                <!-- <span class="des-highlight-matched-text" v-html="highlightMatchedText(searchKey, item.label)"></span> -->
                <t-tooltip :content="item.label" placement="top" :delay="500">
                  <span class="des-highlight-matched-text" v-html="highlightMatchedText(searchKey, item.label)"></span>
                </t-tooltip>
              </li>
            </ul>
            <div v-else style="text-align: center;color: rgba(0, 0, 0, .26);line-height: 32px;">暂无数据</div>
          </template>
        </t-select-input>
        <div class="icon-btn-group">
          <!-- <div v-if="id" @click="handleDownload">
            <des-icon name="des-icon-xiazai" size="16" />
          </div> -->
          <div @click="handleToggleZoom">
            <des-icon :name="expand ? 'des-icon-quanping' : 'des-icon-tuichuquanping'" size="16" style="color: #666;" />
          </div>
          <div @click="handleFormat">
            <des-icon name="des-icon-luoshihuabuju" size="16" style="color: #666;" />
          </div>
          <div @click="handleZoomToFit">
            <des-icon name="des-icon-shuaxin" size="16" style="color: #666;" />
          </div>
        </div>
        <t-popup trigger="click" placement="bottom-right">
          <div class="setting-btn">
            <des-icon name="des-icon-shezhi2" size="16" style="color: #666;"></des-icon>
          </div>
          <template #content>
            <div class="line-type-pop__content">
              <div class="line-type-pop__title">连线方式</div>
              <t-radio-group class="vertical-radio-group" v-model="lineType" @change="handleSetLayout">
                <div>
                  <t-radio value="vertical">从上往下连接</t-radio>
                </div>
                <div>
                  <t-radio value="horizontal">从左往右连接</t-radio>
                </div>
              </t-radio-group>
            </div>
          </template>
        </t-popup>
      </div>
      <div class="paintboard-bottom-tools">
        <t-button variant="outline" style="height: 28px;" @click="handleOpenDebugRecord">调试记录</t-button>
      </div>
    </div>
    <t-drawer :visible.sync="visibleDetail" :closeBtn="true" size="1056px" destroyOnClose>
      <template #header>
        <div class="des-flex-align-center drawer-header">
          <div class="des-flex-align-center">
            <div style="margin-right: 8px;">{{ currentNodeName }}</div>
            <t-tag variant="light"><des-icon name="des-icon-linwujiedian" size="16" :style="{ marginRight: '4px', color: taskTypeColorMapping[currentNode] }"></des-icon>{{taskTypeLabelMapping[currentNode]}}</t-tag>
          </div>
          <t-link v-if="showGoSubFlow" hover="color" theme="primary" :disabled="disabledSubFlow" @click="handleGoSubFlow"><des-icon name='des-micon-gongzuoliu' size="16" slot="prefix-icon" />进入子节点</t-link>
        </div>
      </template>
      <component :is="taskCompMapping[currentNode]" :ref="(el) => setRefMap(el, currentNode)" :currentFlowName="name" :readonly="readonly" @updateProps="updateProcessDefinitionName"></component>
      <template #footer>
        <div style="text-align: right;">
          <t-space :size="8">
            <t-button variant="outline" @click="handleDebugNode" v-if="showDebug(currentNodeData.taskType)">调试</t-button>
            <t-button variant="outline" @click="handleCancelNode">取消</t-button>
            <t-button v-if="!readonly" @click="handleSaveNode">保存</t-button>
          </t-space>
        </div>
      </template>
    </t-drawer>
    <save-work-flow ref="saveWorkFlowRef" @ok="handleSubmitWorkFlow"></save-work-flow>
    <work-flow-info ref="workFlowInfoRef" :data="flowData"></work-flow-info>
    <flow-version ref="flowVersionRef" :spaceId="route.query.spaceId" @refresh="refreshPanelData" :readonly="readonly"></flow-version>
    <view-log ref="viewLogRef" :getScheduleApi="getScheduleLog" :getExecuteApi="getExecuteLog"></view-log>
    <debug-record ref="debugRecordRef" :spaceId="route.query.spaceId" :processCode="flowData.code"></debug-record>
  </div>
</template>

<style lang="less" scoped>
.page-des-work-flow-panel {
  position: relative;
  height: calc(~'100vh - 122px');
  padding: 20px;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    .header-left {
      display: flex;
      align-items: center;
      .draft-wrap {
        height: 22px;
        line-height: 22px;
        margin-left: 24px;
        padding-left: 24px;
        border-left: 1px solid #ccc;
        .draft-text {
          position: relative;
          padding-left: 14px;
          &:before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: #3464E0;
            transform: translateY(-50%);
          }
        }
      }
    }
  }
  .paintboard {
    position: relative;
    height: calc(~'100% - 52px');
    // background-color: #F5F7F9;
    border-radius: 4px;
    .paintboard-inner {
      height: 100%;
    }
    .add-task-btn {
      position: absolute;
      left: 10px;
      top: 10px;
      height: 28px;
    }
    .paintboard-tools {
      display: flex;
      align-items: center;
      position: absolute;
      right: 10px;
      top: 10px;
      gap: 8px;
      :deep(.t-input) {
        height: 28px;
      }
    }
    .paintboard-bottom-tools {
      position: absolute;
      left: 10px;
      bottom: 10px;
      height: 28px;
    }
    .icon-btn-group {
      display: flex;
      height: 28px;
      padding: 7px 4px;
      justify-content: center;
      align-items: center;
      gap: 8px;
      border-radius: 4px;
      background: #FFF;
      flex-shrink: 0;
      & > div {
        position: relative;
        display: flex;
        width: 24px;
        height: 24px;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        &:hover {
          color: var(--des-color-theme);
        }
        &:not(:last-child) {
          &::after {
            content: ' ';
            position: absolute;
            right: -4px;
            top: 6px;
            width: 1px;
            height: 12px;
            background-color: #EEEEEE;
          }
        }
      }
    }
    .setting-btn {
      display: flex;
      width: 28px;
      height: 28px;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      background: #FFF;
      flex-shrink: 0;
      cursor: pointer;
      &:hover {
        color: var(--des-color-theme);
      }
    }
  }
}
.search-items {
  padding: 6px 0;
  li {
    display: flex;
    align-items: center;
    padding: 3px 6px;
    color: #333333;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    cursor: pointer;
    margin-bottom: 2px;
    &:last-child {
      margin-bottom: 0;
    }
    .des-highlight-matched-text {
      display: inline-block;
      max-width: 144px;  /* 限制最大宽度 */
      white-space: nowrap;  /* 禁止换行 */
      overflow: hidden;  /* 超出隐藏 */
      text-overflow: ellipsis;  /* 超出显示省略号 */
      vertical-align: middle;
    }
  }
}
.tools-task-list {
  padding: 8px 0;
  min-width: 200px;
  & > div {
    & > div {
      padding: 5px 12px;
      color: #333333;
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 600;
      line-height: 20px;
    }
    &:not(:last-child) {
      &::after {
        display: block;
        content: ' ';
        height: 1px;
        width: 100%;
        background-color: #EEEEEE;
        margin: 6px 0 5px 0;
      }
    }
  }
  ul {
    padding: 0 12px;
    li {
      display: flex;
      padding: 4px 8px;
      align-items: center;
      gap: 4px;
      align-self: stretch;
      border-radius: 4px;
      background: #F5F7F9;
      height: 28px;
      cursor: pointer;
      &:hover {
        color: var(--des-color-theme);
      }
      &:not(:last-child) {
        margin-bottom: 8px;
      }
    }
  }
}
.line-type-pop__content {
  padding: 4px 8px;
  .line-type-pop__title {
    color: #999999;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    padding: 5px 0;
  }
  .vertical-radio-group {
    display: block;
  }
}
.drawer-header {
  width: 100%;
  justify-content: space-between;
  padding-right: 60px;
}
</style>
