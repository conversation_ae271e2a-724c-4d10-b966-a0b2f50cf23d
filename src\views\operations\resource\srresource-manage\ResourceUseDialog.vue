<template>
  <BasicDialog width="852" :visible.sync="visible" header="资源使用情况" :footer="false" :showFooter="false">
    <t-table row-key="index" :data="list" :columns="columns" max-height="400" />
  </BasicDialog>
</template>

<script setup>
import BasicDialog from '@/components/BasicDialog.vue';
import { defineExpose, ref } from 'vue';
import { useDialog } from '@/views/operations/hooks';

// 弹窗数据
const { visible, openAddDialog } = useDialog({});
const list = ref([]);
const columns = ref([
  {
    colKey: 'serial-number',
    title: '序号',
    width: 61,
  },
  {
    colKey: 'backend',
    title: 'BE的IP/FQDN',
    width: 123,
    ellipsis: true,
  },
  {
    colKey: 'beInUseCpuCores',
    title: '在该be上使用的cpu核数',
    width: 184,
    ellipsis: true,
  },
  {
    colKey: 'beInUseMemBytes',
    title: '在该be上使用的cpu内存',
    width: 184,
    ellipsis: true,
  },
  {
    colKey: 'beRunningQueries',
    title: '在该be上正在运行的SQL数',
    width: 184,
    ellipsis: true,
  },
]);

const show = (data) => {
  list.value = data;
  openAddDialog();
};

defineExpose({ show });
</script>

<style lang="less" scoped>

</style>
