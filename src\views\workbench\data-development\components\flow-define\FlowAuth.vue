<script setup>
import { ref, reactive, computed, defineExpose, defineProps, defineEmits } from 'vue';
import { AddIcon } from 'tdesign-icons-vue';
import UserName from '@/components/UserName.vue';
import { useDataDevelopmentStore } from '@/stores/data-development';
import { useSpacesStore } from '@/stores/spaces';
import { MessagePlugin } from 'tdesign-vue';
import { storeToRefs } from 'pinia';
import { showDialogAlert, showDialogConfirm } from '@/utils/dialog';

const emit = defineEmits(['refresh']);

const props = defineProps({
  spaceId: {
    type: String,
    default: '',
  },
});

const dataDevelopmentStore = useDataDevelopmentStore();
const spacesStore = useSpacesStore();
const { isSpaceOwner } = storeToRefs(spacesStore);

// 当前的权限
const permissionList = ref([]);

const visible = ref(false);
const formRef = ref(null);
const staffSelectorRef = ref(null);
const formData = reactive({
  code: '',
  name: '',
  staff: [],
  authType: [],
});
const choosedStaff = ref([]);

const rules = {
  staff: [{ required: true, message: '请选择授权人员' }],
  authType: [{ required: true, message: '请选择授权类型' }],
};

function hasAuth(operate) {
  if (isSpaceOwner.value) {
    return true;
  }
  return permissionList.value.includes(operate);
}
function hasSameAuth(targetArray, array) {
  return array.some(element => targetArray.includes(element));
}
const authTypeOption = computed(() => {
  const options = [
    { label: '查看', value: 'READ', disabled: !hasAuth('READ') || hasSameAuth(formData.authType, ['RUN', 'EDIT']) },
    { label: '编辑', value: 'EDIT', disabled: !hasAuth('EDIT') || hasSameAuth(formData.authType, ['RUN']) },
    { label: '运行', value: 'RUN', disabled: !hasAuth('RUN') },
  ];
  if (isSpaceOwner.value) {
    options.push({ label: '授予', value: 'GRANT', disabled: !hasAuth('GRANT') });
  }
  return options;
});
const userList = ref([]);
const delDisabled = () => {
  if (userList.value.length === 1) {
    return true;
  }
  if (isSpaceOwner.value) {
    return false;
  }
  if (permissionList.value.includes('GRANT')) {
    return false;
  }
  return true;
};
const colums = ref([
  {
    title: '成员',
    colKey: 'user',
    width: 188,
    cell: (h, { row }) => (<UserName fullName={row.user.staffName}></UserName>),
  },
  {
    title: '用户组',
    colKey: 'spaceUserGroupList',
    ellipsis: true,
    cell: (h, { row }) => (<span>{row.spaceUserGroupList?.map(item => item.name).join('、') || '-'}</span>),
  },
  {
    title: '权限',
    colKey: 'permissionList',
    cell: (h, { row }) => (<span>{getAuthName(row.permissionList)}</span>),
  },
  {
    title: '操作',
    colKey: 'operation',
    width: 80,
    cell: (h, { row }) => (<t-link hover="color" theme="danger" onClick={() => handleRemove(row)} disabled={ delDisabled(row) }>移除</t-link>),
  },
]);

const authNameMap = {
  EDIT: '编辑',
  RUN: '运行',
  READ: '查看',
  GRANT: '授予',
};
function getAuthName(list = []) {
  return list.map(item => authNameMap[item]).join('、');
}

async function handleRemove(row) {
  console.log('移除', row);
  if (row.spaceUserGroupList?.length) {
    const permission = new Set();
    row.spaceUserGroupList.forEach((item) => {
      item.permissionList?.forEach(v => permission.add(v));
      if (item.isSystemDefault === 1) {
        ['RUN', 'READ', 'EDIT', 'GRANT'].forEach(v => permission.add(v));
      }
    });
    // 查出可移除的权限
    const removeablePermission = row.permissionList.filter(item => !permission.has(item));
    const dialogConfig = {
      title: `移除成员「${row.user.staffName}」`,
      body: null,
      width: '440px',
    };
    if (removeablePermission.length) {
      dialogConfig.confirmText = '移除';
      dialogConfig.body = () => (
        <div style="padding-left: 26px;">
          <p>
            无法移除该成员所在用户组权限: { Array.from(permission).map(item => authNameMap[item])
            .join('、') }
          </p>
          <p>可移除的个人权限: { removeablePermission.map(item => authNameMap[item]).join('、') }</p>
        </div>
      );
      const { type } =  await showDialogConfirm(dialogConfig);
      if (type === 'cancel') return;
    } else {
      dialogConfig.body = () => (
        <div style="padding-left: 26px;">
          <p>
            无法移除该成员所在用户组权限: { Array.from(permission).map(item => authNameMap[item])
            .join('、') }
          </p>
        </div>
      );
      // 用户组有全部权限
      showDialogAlert(dialogConfig);
      return;
    }
  }
  const { data } = await dataDevelopmentStore.fetchFlowDefineAuthRevoke(props.spaceId, formData.code, { staffId: row.user.staffId });
  data && MessagePlugin('success', '移除授权成功');
  fetchGrantors();
}

// 提交前检查权限的方法
async function checkPermissionsBeforeSubmit(spaceId, staffList) {
  for (const staff of staffList) {
    const { data } = await spacesStore.checkSpaceIsOwnerByStaff(spaceId, staff.StaffID);
    if (data) {
      MessagePlugin.error(`${staff.StaffName}是空间负责人，默认已有全部权限，不需要添加`);
      return false;
    }
  }
  return true;
}

function handleAdd() {
  formRef.value.validate().then(async (result) => {
    if (result === true) {
      const canSubmit = await checkPermissionsBeforeSubmit(props.spaceId, choosedStaff.value);
      if (!canSubmit) return;

      choosedStaff.value.forEach((staff) => {
        // 有编辑权限默认+READ
        let permissionList = formData.authType;

        if (formData.authType.includes('EDIT')) {
          const setArray = new Set(formData.authType);
          setArray.add('READ');
          permissionList = [...setArray];
        }
        userList.value.push({
          user: {
            staffId: staff.StaffID,
            engName: staff.EngName,
            staffName: staff.StaffName,
          },
          permissionList,
        });
      });
      choosedStaff.value = [];
      formData.authType = [];
      formData.staff = [];
      staffSelectorRef.value.clearSelected();
      formRef.value.reset();
      handleGrantAuth();
    }
  });
}

function getSaveData() {
  return {
    userList: userList.value,
  };
}

async function fetchGrantors() {
  const { data = [] } = await dataDevelopmentStore.fetchFlowDefineAuthGrantors(props.spaceId, formData.code);
  userList.value = data;
}

function handleChangeStaff(staffs) {
  choosedStaff.value = staffs;
}

function handleClose() {
  close();
}

async function handleGrantAuth() {
  const subData = getSaveData();
  const { data } = await dataDevelopmentStore.fetchFlowDefineAuthGrant(props.spaceId, formData.code, subData);
  data && MessagePlugin('success', '授权成功');
  fetchGrantors();
}

function open(item) {
  visible.value = true;
  if (item) {
    formData.code = item.code;
    formData.name = item.name;
    permissionList.value = item.permissionList;
    fetchGrantors();
  }
}
function close() {
  visible.value = false;
  staffSelectorRef.value.clearSelected();
  formData.code = '';
  formData.name = '';
  formData.staff = [];
  choosedStaff.value = [];
  formData.auth = [];
  formRef.value.reset();
  emit('refresh');
}

function handleChangeAuthType(value, context) {
  const { current, type } = context;
  const array = value.filter(item => item === 'GRANT');
  if (current === 'RUN') {
    formData.authType = type === 'check' ? ['RUN', 'EDIT', 'READ', ...array] : [...array];
  }
  if (current === 'EDIT') {
    formData.authType = type === 'check' ? ['EDIT', 'READ', ...array] : [...array];
  }
}

defineExpose({ open, close });
</script>

<template>
  <t-drawer
    size="696px"
    :visible.sync="visible"
    header="授权"
    :closeBtn="true"
    :cancelBtn="null"
    :confirmBtn="null"
    destroyOnClose
    @close="handleClose"
    class="des-data-development__flow-define__flow-auth-drawer"
  >
    <t-space :gap="20" direction="vertical" style="width:100%;">
      <t-form :data="formData" ref="formRef" labelWidth="106px" :rules="rules" labelAlign="top">
        <t-form-item label="工作流名称">
          <div style="color: #333;">{{ formData.name }}</div>
        </t-form-item>
        <t-form-item label="授权成员" name="staff">
          <sdc-staff-selector ref="staffSelectorRef" v-model="formData.staff" @change="handleChangeStaff" multiple style="width:100%;" placeholder="请选择人员" modalClass="des-sdc-modal--fix" size="small"></sdc-staff-selector>
        </t-form-item>
        <t-form-item label="授权类型" name="authType">
          <t-checkbox-group v-model="formData.authType" :options="authTypeOption" @change="handleChangeAuthType" />
        </t-form-item>
      </t-form>
      <t-button variant="outline" theme="primary" @click="handleAdd"><add-icon slot="icon" />添加</t-button>
    </t-space>
    <t-divider></t-divider>
    <t-space :gap="16" direction="vertical">
      <h3>已授权的成员</h3>
      <t-table row-key="staffId" :data="userList" :columns="colums" />
    </t-space>
  </t-drawer>
</template>

<style lang="less">
.des-data-development__flow-define__flow-auth-drawer {
  .t-drawer__body {
    padding: 24px;
  }
}
</style>
