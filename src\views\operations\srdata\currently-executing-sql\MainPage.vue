<template>
  <div style="height: 100%">
    <PageLayout>
      <template #search>
        <BasicSearch v-model="searchKey" placeholder="查询ID/连接ID/用户/查询SQL/资源组/自定义查询ID..." @reset="handleReset" @change="searchChange">
          <template #prefix>
            <t-select
              v-model="datasourceId"
              :options="datasourceOptions"
              placeholder="数据源"
              filterable
              @change="searchChange"
            ></t-select>
          </template>
        </BasicSearch>
      </template>

      <template #table>
        <t-table rowKey="id" :data="tableData" :columns="columns" :pagination="pagination" max-height="68vh">
          <template #empty v-if="!datasourceId">
            <no-data text="请先选择数据源"></no-data>
          </template>
        </t-table>
      </template>
    </PageLayout>

    <ViewResourceDialog ref="dialogRef"></ViewResourceDialog>
  </div>
</template>

<script setup lang="jsx" name="currently-executing-sql">
import { ref, defineProps } from 'vue';
import { useSearch, usePagination } from '@/hooks';
import { PageLayout } from '@/views/operations/components';
import BasicSearch from '@/components/BasicSearch.vue';
import ViewResourceDialog from './ViewResourceDialog.vue';
import NoData from '@/components/NoData.vue';
import { useOperationsStore } from '@/stores/operations';
import { to } from '@/utils/util';
import { MessagePlugin } from 'tdesign-vue';
import { showDialogConfirm } from '@/utils/dialog';

defineProps({
  datasourceOptions: {
    type: Array,
    default: () => [],
  },
});

const operationsStore = useOperationsStore();

const dialogRef = ref(null);

const datasourceId = ref('');

const columns = ref([
  { colKey: 'serial-number', width: 61, title: '序号', fixed: 'left' },
  {
    colKey: 'startup',
    title: '开始时间',
    width: 180,
    ellipsis: true,
  },
  {
    colKey: 'feIp',
    title: '所在FE节点',
    width: 111,
    ellipsis: true,
  },
  {
    colKey: 'queryId',
    title: '查询ID',
    width: 143,
    ellipsis: true,
  },
  {
    colKey: 'connectionId',
    title: '连接ID',
    width: 133,
    ellipsis: true,
  },
  {
    colKey: 'database',
    title: '数据库',
    width: 121,
    ellipsis: true,
  },
  {
    colKey: 'user',
    title: '用户',
    width: 164,
    ellipsis: true,
  },
  {
    colKey: 'scanBytes',
    title: '扫描的数据大小',
    width: 149,
    ellipsis: true,
  },
  {
    colKey: 'scanRows',
    title: '扫描的行数',
    width: 119,
    ellipsis: true,
  },
  {
    colKey: 'memoryUsage',
    title: '内存使用量',
    width: 102,
    ellipsis: true,
  },
  {
    colKey: 'cpuTime',
    title: 'CPU时间',
    width: 101,
    ellipsis: true,
  },
  {
    colKey: 'execute',
    title: '执行时间',
    width: 187,
    ellipsis: true,
  },
  {
    colKey: 'customQueryId',
    title: '自定义查询ID',
    width: 136,
    ellipsis: true,
  },
  {
    colKey: 'resourceGroup',
    title: '资源组',
    width: 131,
    ellipsis: true,
  },
  {
    colKey: 'statement',
    title: '查询SQL',
    width: 217,
    ellipsis: true,
  },
  {
    title: '操作',
    colKey: 'operate',
    width: 188,
    fixed: 'right',
    cell: (h, { row }) => (
      <t-space>
        <t-link theme="primary" hover="color" onClick={() => handleView(row) }>
          资源消耗查看
        </t-link>
        <t-link theme="primary" hover="color" onClick={() => handleStop(row)}>
          终止SQL
        </t-link>
      </t-space>
    ),
  },
]);

const tableData = ref([]);

const fetchData = async () => {
  if (!datasourceId.value) return;
  const { current: pageNum, pageSize } = pagination;
  const params = {
    pageNum,
    pageSize,
    queryData: {
      queryKey: searchKey.value,
    },
  };
  const [err, data] = await to(operationsStore.fetchCurQueryList(datasourceId.value, params));
  if (err) {
    return;
  }
  const { total, list } = data;
  setPaginationTotal(total);
  tableData.value = list || [];
};

const { searchKey, setSearchKey, onSearch } = useSearch(fetchData);
const { pagination, setPaginationCurrent, setPaginationTotal } = usePagination(fetchData);

const searchChange = () => {
  setPaginationCurrent(1);
  onSearch();
};

const handleReset = () => {
  setSearchKey('');
  setPaginationCurrent(1);
  fetchData();
};

const handleView = () => {
  MessagePlugin.error('接口未开发');
  // dialogRef.value.openAddDialog(row);
};

const handleStop = async (row) => {
  const { type } = await showDialogConfirm({
    title: '确定终止SQL吗？',
    body: '终止后不可恢复',
    width: '440px',
  });
  if (type === 'cancel') return;
  const [err, data] = await to(operationsStore.fetchCurQueryStop(datasourceId.value, row.queryId));
  if (err) {
    return;
  }
  if (data) {
    fetchData();
    MessagePlugin('success', '操作成功');
  }
};
</script>

<style lang="less" scoped>
:deep(.basic-search-space__wrap) {
  .basic-search-input {
    width: 458px;
  }
}
</style>
