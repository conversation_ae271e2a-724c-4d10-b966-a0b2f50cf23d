<script setup>
import { ref, inject, onMounted, onBeforeUnmount } from 'vue';
import TaskListPopup from '../TaskListPopup.vue';
import emitter, { EVENT_NAMES } from '@/utils/emitter';
import { isSafari, formatTime, secondConvertTime } from '@/utils/util';
import { showDebug } from '../../utils/task';
import { taskTypeLabelMapping } from '../../utils/task-type';
import { PlayCircleIcon } from 'tdesign-icons-vue';

const getNode = inject('getNode');
const getGraph = inject('getGraph');
// 由于X6用到了foreignObject，而foreignObject在safari中存在样式兼容问题，
// 因此需要判断是否是safari浏览器，如果是，则去除一切定位和rgba样式，必须阉割toolts
const safari = isSafari();

const data = ref({
  // 节点名称
  title: '',
  // 节点类型
  type: '',
  // 节点颜色
  color: '',
  // 所属面板
  panel: '',
  // 对齐方式
  direction: 'horizontal',
  readonly: false,
  // // 任务实例的信息, 从工作流实例标识点击进来才有
  taskInfo: null,
});

function getStatusIcon(status) {
  return {
    unknown: 'des-icon-line-jiazaizhong',
    running: 'des-icon-line-jiazaizhong',
    submit: 'des-icon-line-jiazaizhong',
    failed: 'des-icon-guanbiyuanxing',
    success: 'des-icon-fill-wanchengdagouzhengque',
    pause: 'des-icon-guanbiyuanxing',
    dispatch: 'des-icon-line-jiazaizhong',
    kill: 'des-icon-guanbiyuanxing',
    forced_success: 'des-icon-fill-wanchengdagouzhengque',
    delay_execution: 'des-icon-line-jiazaizhong',
    need_fault_tolerance: 'des-icon-guanbiyuanxing',
    stop: 'des-icon-guanbiyuanxing',
  }[status];
}

function getStatusColor(status) {
  return {
    unknown: '#3464e0',
    running: '#3464e0',
    submit: '#3464e0',
    failed: '#f81d22',
    success: '#0ad0b6',
    pause: '#f81d22',
    dispatch: '派送',
    kill: '#f81d22',
    forced_success: '#0ad0b6',
    delay_execution: '#3464e0',
    need_fault_tolerance: '#f81d22',
    stop: '#f81d22',
  }[status];
}

function getStatusLabel(status) {
  return {
    unknown: '未知',
    running: '运行中',
    submit: '提交',
    failed: '失败',
    success: '成功',
    pause: '暂停',
    dispatch: '派送',
    kill: 'kill',
    forced_success: '强制成功',
    delay_execution: '推迟执行',
    need_fault_tolerance: '需要容错',
    stop: '停止',
  }[status];
}

function setValue(obj = {}) {
  const { title, type, color, panel, direction, readonly, taskInfo } = obj;
  data.value = {
    title,
    type,
    color,
    panel,
    direction,
    readonly,
    taskInfo,
  };
}

function handleMouseEnter() {
  const node = getNode();
  const ports = node.getPorts() || [];
  ports.forEach((port) => {
    node.setPortProp(port.id, 'attrs/circle', {
      fill: '#fff',
      stroke: '#25dc93',
    });
  });
}
function handleMouseLeave() {
  const node = getNode();
  // 获取该节点下的所有连接桩
  const ports = node.getPorts() || [];
  ports.forEach((port) => {
    node.setPortProp(port.id, 'attrs/circle', {
      fill: 'transparent',
      stroke: 'transparent',
    });
  });
}

function handleViewLog() {
  const node = getNode();
  emitter.emit(EVENT_NAMES.WORK_FLOW_PANEL.VIEW_TASK_LOG, {
    panel: data.value.panel,
    data: node,
  });
}

function handleEdit() {
  const node = getNode();
  emitter.emit(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_NODE_EDIT, {
    panel: data.value.panel,
    data: node,
  });
}
function handleView() {
  const node = getNode();
  emitter.emit(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_NODE_VIEW, {
    panel: data.value.panel,
    data: node,
  });
}

function handleDelete() {
  const graph = getGraph();
  const node = getNode();
  graph.removeNode(node);
  emitter.emit(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_NODE_DELETE, {
    panel: data.value.panel,
    data: node,
  });
}
function handleAddNode(direction = 'right', parent, item) {
  const node = getNode();
  emitter.emit(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_NODE_ADD, {
    panel: data.value.panel,
    data: { direction, node, parent, item },
  });
}
function handleDebug() {
  const node = getNode();
  emitter.emit(EVENT_NAMES.WORK_FLOW_PANEL.GRAPH_NODE_DEBUG, {
    panel: data.value.panel,
    data: node,
  });
}

const domRef = ref(null);
// 更新节点尺寸方法
function updateNodeSize(node) {
  const { offsetWidth, offsetHeight } = domRef.value;
  if (offsetWidth > 0 && offsetHeight > 0) {
    node.resize(Math.max(offsetWidth, 240), offsetHeight);
  }
}

const resizeObserver = ref(null);
onMounted(() => {
  const node = getNode();
  setValue(node.getData());
  node.on('change:data', ({ current }) => {
    setValue(current);
  });

  const graph = getGraph();
  graph.on('node:mouseenter', (e) => {
    if (e.node.id === node.id) {
      handleMouseEnter();
    }
  });
  graph.on('node:mouseleave', (e) => {
    if (e.node.id === node.id) {
      handleMouseLeave();
    }
  });

  // 监听DOM尺寸变化
  resizeObserver.value = new ResizeObserver(() => updateNodeSize(node));
  resizeObserver.value.observe(domRef.value);
});

onBeforeUnmount(() => {
  // 停止并清理ResizeObserver
  if (resizeObserver.value) {
    resizeObserver.value.disconnect();
    resizeObserver.value = null;
  }
});
</script>

<template>
  <div :class="['des-graph-job-node', data.direction, {'safari': safari}]" ref="domRef">
    <div class="des-graph-job-node__content" @dblclick="handleView">
      <div class="title">
        <des-icon name="des-icon-linwujiedian" size="16" :style="{ marginRight: '12px', color: data.color }"></des-icon>
        <!-- <ellipsis-with-tooltip :text="data.title"></ellipsis-with-tooltip> -->
        <span>{{ data.title }}</span>
      </div>
      <div class="desc">{{ taskTypeLabelMapping[data.type] || data.type }}</div>
    </div>
    <template v-if="data.taskInfo">
      <div class="des-graph-job-node__task-info" @click="handleViewLog">
        <t-popup :overlayInnerStyle="{ background: '#444', color: '#fff' }">
          <div class="task-status">
            <des-icon :name="getStatusIcon(data.taskInfo.taskState)" :style="{ color: getStatusColor(data.taskInfo.taskState) }" size="16"></des-icon>
            <span style="margin-left: 4px;">{{ getStatusLabel(data.taskInfo.taskState) }}</span>
          </div>
          <template #content>
            <ul>
              <li>状态：{{ getStatusLabel(data.taskInfo.taskState) }}</li>
              <li>运行时长：{{ secondConvertTime(data.taskInfo.costTime / 1000) }}</li>
              <li>重跑次数：{{ data.taskInfo.retryTimes }}</li>
              <li>提交时间：{{ formatTime(data.taskInfo.submitTime) }}</li>
              <li>开始时间：{{ formatTime(data.taskInfo.startTime) }}</li>
              <li>结束时间：{{ formatTime(data.taskInfo.endTime) }}</li>
            </ul>
          </template>
        </t-popup>
      </div>
    </template>
    <template v-if="!data.readonly && !safari">
      <div class="des-graph-job-node__tools">
        <div class="icon-btn" @click.prevent.stop="handleDebug" v-if="showDebug(data.type)">
          <PlayCircleIcon />
        </div>
        <div class="icon-btn" @click.prevent.stop="handleEdit">
          <des-icon name="des-icon-bianjixiugai" size="14"></des-icon>
        </div>
        <div class="icon-btn is-delete" @click.prevent.stop="handleDelete">
          <des-icon name="des-icon-shanshu" size="14"></des-icon>
        </div>
      </div>
      <task-list-popup :placement="data.direction === 'horizontal' ? 'left' : 'top'" trigger="hover" @clickNode="(parent, child) => handleAddNode('left', parent, child)">
        <div class="des-graph-job-node__left-btn">
          <des-icon name="des-icon-fill-xincengjia" size="16"></des-icon>
        </div>
      </task-list-popup>
      <task-list-popup :placement="data.direction === 'horizontal' ? 'right' : 'bottom'" trigger="hover" @clickNode="(parent, child) => handleAddNode('right', parent, child)">
        <div class="des-graph-job-node__right-btn">
          <des-icon name="des-icon-fill-xincengjia" size="16"></des-icon>
        </div>
      </task-list-popup>
    </template>
  </div>
</template>

<style lang="less" scoped>
.des-graph-job-node {
  &:not(.safari) {
    position: relative;
    box-shadow: 0 9px 28px 0 rgba(0, 0, 0, 0.05);
    .des-graph-job-node__content {
      position: relative;
      display: flex;
      flex-direction: column;
      width: 100%;
    }
  }
  min-width: 240px;
  width: fit-content;  // 根据内容自适应宽度
  max-width: auto;
  height: 68px;
  border-radius: 6px;
  border: 1.5px solid #EEE;
  background: #FFF;

  display: inline-flex;
  &:hover {
    border-color: var(--des-color-theme);
    // .des-graph-job-node__tools {
    //   visibility: visible;
    // }
  }
  &.vertical {
    .des-graph-job-node__left-btn {
      left: 50%;
      top: -24px;
      transform: translate(-50%, 0);
    }
    .des-graph-job-node__right-btn {
      left: 50%;
      bottom: -24px;
      top: unset;
      right: unset;
      transform: translate(-50%, 0);
    }
  }
  .des-graph-job-node__content {
    // position: relative;
    padding: 12px 16px;
  }
  .title {
    display: flex;
    align-items: center;
    color: #333333;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
    margin-bottom: 4px;
    flex: 1;  // 占据剩余空间
    min-width: 0;  // 允许内容压缩
    span {
      white-space: nowrap;  // 禁止换行
      overflow: visible;    // 允许内容溢出
    }
  }
  .desc {
    color: #999999;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    padding-left: 28px;
  }
  .des-graph-job-node__task-info {
    position: absolute;
    bottom: 100%;
    left: 0;
    gap: 4px;
    padding-bottom: 4px;
    cursor: pointer;
    .task-status {
      padding: 4px;
      border-radius: 3px;
      background: #fff;
    }
  }
  .des-graph-job-node__tools {
    position: absolute;
    bottom: 100%;
    right: 0;
    display: flex;
    gap: 4px;
    visibility: hidden;
    padding-bottom: 4px;
    .icon-btn {
      display: flex;
      width: 32px;
      height: 32px;
      justify-content: center;
      align-items: center;
      border-radius: 6px;
      border: 1px solid #EEE;
      background: #FFF;
      box-shadow: 0 9px 28px 0 rgba(0, 0, 0, 0.05);
      cursor: pointer;
      &:hover {
        color: var(--des-color-theme);
      }
      &.is-delete {
        color: #F81D22;
      }
    }
  }
  .des-graph-job-node__left-btn,
  .des-graph-job-node__right-btn {
    visibility: hidden;
    position: absolute;
    top: 50%;
    width: 16px;
    height: 16px;
    cursor: pointer;
    transform: translate(0, -50%);
  }
  .des-graph-job-node__left-btn {
    left: -24px;
  }
  .des-graph-job-node__right-btn {
    right: -24px;
  }
}
</style>

<style lang="less">
.x6-node-selected {
  .des-graph-job-node {
    border-color: #3464E0;
    &:not(.safari) {
      position: relative;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(52, 100, 224, 0.1);
        border-radius: 5px;  // 比容器小1px，避免溢出
        z-index: 0;
      }
      .des-graph-job-node__content {
        position: relative;
        z-index: 1;
      }
    }
    .des-graph-job-node__tools,
    .des-graph-job-node__left-btn,
    .des-graph-job-node__right-btn {
      visibility: visible;
    }
  }
}
.x6-edge-selected path:nth-child(2) {
  stroke-width: 2px !important;
}
</style>
